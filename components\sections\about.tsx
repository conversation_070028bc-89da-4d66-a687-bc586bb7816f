"use client"

import { useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Award, Users, Building, TrendingUp, ArrowRight, CheckCircle, Target, Heart } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

const stats = [
  { icon: Users, value: "500+", label: "Happy Clients", color: "text-blue-600" },
  { icon: Building, value: "50+", label: "Projects Completed", color: "text-green-600" },
  { icon: Award, value: "15+", label: "Years Experience", color: "text-purple-600" },
  { icon: TrendingUp, value: "₹100Cr+", label: "Property Value", color: "text-orange-600" },
]

const values = [
  {
    icon: Target,
    title: "Excellence",
    description: "We strive for perfection in every project and client interaction.",
  },
  {
    icon: Heart,
    title: "Trust",
    description: "Building lasting relationships through transparency and integrity.",
  },
  {
    icon: Award,
    title: "Innovation",
    description: "Embracing modern solutions to enhance your real estate experience.",
  },
]

export default function About() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-textured-light">
      <div className="container mx-auto px-4">
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-20">
          {/* Left Content */}
          <div className="animate-on-scroll opacity-0 translate-x-8">
            <div className="inline-flex items-center space-x-2 bg-burgundy-100 text-burgundy-700 rounded-full px-4 py-2 text-sm font-medium mb-6">
              <Building size={16} />
              <span>About Dwelling Desire</span>
            </div>

            <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
              Transforming{" "}
              <span className="relative">
                <span className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 bg-clip-text text-transparent">
                  Dreams
                </span>
                <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-full"></div>
              </span>{" "}
              Into Reality
            </h2>

            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              For over 15 years, Dwelling Desire has been Ahmedabad's trusted partner in luxury real estate. We
              specialize in creating exceptional living experiences through premium properties and personalized service.
            </p>

            <div className="space-y-4 mb-8">
              {[
                "Expert market knowledge and insights",
                "Personalized service for every client",
                "Comprehensive property solutions",
                "Transparent and ethical practices",
              ].map((item, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle className="text-green-500 flex-shrink-0" size={20} />
                  <span className="text-gray-700">{item}</span>
                </div>
              ))}
            </div>

            <Button
              size="lg"
              className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 hover:from-burgundy-700 hover:to-burgundy-800 text-white px-8 py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 group"
              asChild
            >
              <Link href="/about">
                Learn More About Us
                <ArrowRight className="ml-2 group-hover:translate-x-1 transition-transform" size={20} />
              </Link>
            </Button>
          </div>

          {/* Right Content - Image */}
          <div className="animate-on-scroll opacity-0 translate-x-8 delay-200">
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-burgundy-600/20 to-burgundy-700/20 rounded-3xl transform rotate-3"></div>
              <Image
                src="/images/about.webp"
                alt="About Dwelling Desire"
                width={600}
                height={500}
                className="relative rounded-3xl shadow-2xl"
              />

              {/* Floating Stats Card */}
              <div className="absolute -bottom-8 -left-8 bg-white rounded-2xl p-6 shadow-xl border border-gray-100">
                <div className="text-3xl font-bold text-burgundy-600 mb-1">15+</div>
                <div className="text-sm text-gray-600">Years of Excellence</div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Section */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {stats.map((stat, index) => {
            const IconComponent = stat.icon
            return (
              <Card
                key={stat.label}
                className={`animate-on-scroll opacity-0 translate-y-8 text-center border-0 bg-textured-white hover:shadow-lg transition-all duration-300`}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <CardContent className="p-8">
                  <div className={`w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-2xl flex items-center justify-center`}>
                    <IconComponent className={stat.color} size={28} />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600 font-medium">{stat.label}</div>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Values Section */}
        <div className="animate-on-scroll opacity-0 translate-y-8 delay-400">
          <div className="text-center mb-12">
            <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Our Core{" "}
              <span className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 bg-clip-text text-transparent">
                Values
              </span>
            </h3>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              The principles that guide everything we do at Dwelling Desire
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {values.map((value, index) => {
              const IconComponent = value.icon
              return (
                <Card
                  key={value.title}
                  className="text-center border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group"
                >
                  <CardContent className="p-8">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <IconComponent className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">{value.title}</h4>
                    <p className="text-gray-600 leading-relaxed">{value.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </div>
    </section>
  )
}
