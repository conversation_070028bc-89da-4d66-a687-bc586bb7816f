"use client"

import { useEffect, useRef } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Home, Key, Building2, Handshake, TrendingUp, FileText, ArrowRight } from "lucide-react"
import Link from "next/link"

const services = [
  {
    icon: Home,
    title: "Buy",
    description: "Find your perfect home with our extensive property listings and expert guidance.",
    features: ["Property Search", "Market Analysis", "Negotiation Support"],
    color: "from-blue-500 to-blue-600",
  },
  {
    icon: Key,
    title: "Rent",
    description: "Discover rental properties that match your lifestyle and budget requirements.",
    features: ["Rental Listings", "Tenant Screening", "Lease Management"],
    color: "from-green-500 to-green-600",
  },
  {
    icon: Building2,
    title: "Lease",
    description: "Commercial leasing solutions for businesses looking to establish or expand.",
    features: ["Commercial Spaces", "Flexible Terms", "Location Analysis"],
    color: "from-purple-500 to-purple-600",
  },
  {
    icon: FileText,
    title: "Pre-lease",
    description: "Get early access to upcoming projects with pre-launch booking advantages.",
    features: ["Early Bird Offers", "Payment Plans", "Construction Updates"],
    color: "from-orange-500 to-orange-600",
  },
  {
    icon: Handshake,
    title: "Sell",
    description: "Maximize your property value with our comprehensive selling services.",
    features: ["Property Valuation", "Marketing Strategy", "Quick Sale Process"],
    color: "from-red-500 to-red-600",
  },
  {
    icon: TrendingUp,
    title: "Invest",
    description: "Smart investment opportunities in high-growth real estate markets.",
    features: ["ROI Analysis", "Market Insights", "Portfolio Management"],
    color: "from-yellow-500 to-yellow-600",
  },
]

export default function Services() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16 animate-on-scroll opacity-0 translate-y-8">
          <div className="inline-flex items-center space-x-2 bg-burgundy-100 text-burgundy-700 rounded-full px-4 py-2 text-sm font-medium mb-4">
            <Building2 size={16} />
            <span>Our Services</span>
          </div>

          <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Complete Real Estate{" "}
            <span className="relative">
              <span className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 bg-clip-text text-transparent">
                Solutions
              </span>
              <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-full"></div>
            </span>
          </h2>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            From buying your first home to building an investment portfolio, we provide comprehensive real estate
            services tailored to your unique needs.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-6 mb-12">
          {services.map((service, index) => {
            const IconComponent = service.icon
            return (
              <Card
                key={service.title}
                className={`animate-on-scroll opacity-0 translate-y-8 group hover:shadow-xl transition-all duration-500 border-0 bg-white/80 backdrop-blur-sm hover:-translate-y-2 h-fit`}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <CardContent className="p-5 lg:p-6">
                  <div
                    className={`w-10 h-10 lg:w-12 lg:h-12 bg-gradient-to-r ${service.color} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}
                  >
                    <IconComponent className="text-white" size={18} />
                  </div>

                  <h3 className="text-lg lg:text-xl font-bold text-gray-900 mb-3 group-hover:text-burgundy-600 transition-colors">
                    {service.title}
                  </h3>

                  <p className="text-gray-600 mb-4 leading-relaxed text-sm">{service.description}</p>

                  <ul className="space-y-1.5 mb-4">
                    {service.features.map((feature) => (
                      <li key={feature} className="flex items-center text-xs text-gray-500">
                        <div className="w-1 h-1 bg-burgundy-500 rounded-full mr-2"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>

                  <Button
                    variant="ghost"
                    className="text-burgundy-600 hover:text-burgundy-700 hover:bg-burgundy-50 p-0 h-auto font-semibold group/btn text-sm"
                    asChild
                  >
                    <Link href={`/services/${service.title.toLowerCase()}`}>
                      Learn More
                      <ArrowRight className="ml-2 group-hover/btn:translate-x-1 transition-transform" size={14} />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* CTA Section */}
        <div className="animate-on-scroll opacity-0 translate-y-8 delay-600">
          <div className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-3xl p-8 lg:p-12 text-center text-white">
            <h3 className="text-3xl lg:text-4xl font-bold mb-4">Need Expert Guidance?</h3>
            <p className="text-xl text-burgundy-100 mb-8 max-w-2xl mx-auto">
              Choose the perfect service for your needs. Our team is here to make your real estate dreams come true.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-white text-burgundy-600 hover:bg-gray-100 px-8 py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                asChild
              >
                <Link href="/contact">
                  Get Free Consultation
                  <ArrowRight className="ml-2" size={20} />
                </Link>
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-2 border-white/30 text-white hover:bg-white/10 hover:border-white/50 px-8 py-4 rounded-full font-semibold transition-all duration-300 bg-transparent"
                asChild
              >
                <Link href="/projects">
                  Browse Properties
                  <Building2 className="ml-2" size={20} />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
