"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import {
  Calculator,
  Scale,
  PieChart,
  TrendingUp,
  Home,
  DollarSign,
  Percent,
  Calendar,
  Target,
  BarChart3,
  ArrowRight,
  IndianRupee,
  Ruler,
  CreditCard
} from "lucide-react"

// Utility function to get appropriate unit indicator and formatted display
const getUnitInfo = (value: string) => {
  const numValue = parseFloat(value)
  if (isNaN(numValue) || numValue === 0) return { indicator: "", display: "" }

  if (numValue >= 10000000) {
    const crores = (numValue / 10000000).toFixed(2)
    return {
      indicator: "Cr",
      display: `${crores} Cr`,
      color: "bg-purple-100 text-purple-700 border-purple-200"
    }
  }
  if (numValue >= 100000) {
    const lakhs = (numValue / 100000).toFixed(2)
    return {
      indicator: "L",
      display: `${lakhs} L`,
      color: "bg-blue-100 text-blue-700 border-blue-200"
    }
  }
  if (numValue >= 1000) {
    const thousands = (numValue / 1000).toFixed(2)
    return {
      indicator: "T",
      display: `${thousands} T`,
      color: "bg-green-100 text-green-700 border-green-200"
    }
  }
  return { indicator: "", display: "", color: "" }
}

// Utility function to format currency with unit indicator for display
const formatCurrencyWithUnit = (amount: number) => {
  // Format with Indian number system (lakhs and crores)
  const formatIndianNumber = (num: number) => {
    return num.toLocaleString('en-IN')
  }

  if (amount >= 10000000) {
    return `₹${formatIndianNumber(amount)} Cr`
  }
  if (amount >= 100000) {
    return `₹${formatIndianNumber(amount)} L`
  }
  if (amount >= 1000) {
    return `₹${formatIndianNumber(amount)} T`
  }
  return `₹${formatIndianNumber(amount)}`
}

// Enhanced Input Component with Unit Indicator
const CurrencyInput = ({
  value,
  onChange,
  placeholder,
  label,
  className = ""
}: {
  value: string
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  placeholder: string
  label: string
  className?: string
}) => {
  const unitInfo = getUnitInfo(value)

  return (
    <div className="space-y-2">
      <label className="block text-sm font-semibold text-gray-700">{label}</label>
      <div className="relative">
        <Input
          type="number"
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className={`h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0 ${unitInfo.indicator ? 'pr-16' : ''} ${className}`}
        />
        {unitInfo.indicator && (
          <div className={`absolute right-3 top-1/2 transform -translate-y-1/2 px-2 py-1 rounded-md text-xs font-semibold border ${unitInfo.color} transition-all duration-200`}>
            {unitInfo.indicator}
          </div>
        )}
      </div>
      {unitInfo.display && (
        <p className="text-xs text-gray-500 mt-1">
          ≈ {unitInfo.display}
        </p>
      )}
    </div>
  )
}

export default function CalculatorPageContent() {
  const [activeCalculator, setActiveCalculator] = useState("emi")
  
  // EMI Calculator State
  const [emiData, setEmiData] = useState({
    loanAmount: "",
    interestRate: "",
    tenure: ""
  })
  const [emiResult, setEmiResult] = useState<any>(null)

  // Rent vs Buy Calculator State
  const [rentBuyData, setRentBuyData] = useState({
    propertyPrice: "",
    monthlyRent: "",
    downPayment: "",
    loanInterestRate: "",
    loanTenure: "",
    propertyAppreciation: "",
    rentIncrease: ""
  })
  const [rentBuyResult, setRentBuyResult] = useState<any>(null)

  // ROI Calculator State
  const [roiData, setRoiData] = useState({
    propertyValue: "",
    monthlyRental: "",
    maintenanceCost: "",
    propertyTax: "",
    appreciationRate: ""
  })
  const [roiResult, setRoiResult] = useState<any>(null)

  // Area Conversion State
  const [areaData, setAreaData] = useState({
    value: "",
    fromUnit: "sqft",
    toUnit: "sqm"
  })
  const [areaResult, setAreaResult] = useState<any>(null)

  // Loan Eligibility State
  const [loanEligibilityData, setLoanEligibilityData] = useState({
    monthlyIncome: "",
    monthlyExpenses: "",
    existingEMI: "",
    interestRate: "",
    tenure: "",
    age: ""
  })
  const [loanEligibilityResult, setLoanEligibilityResult] = useState<any>(null)

  useEffect(() => {
    // Animation setup
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const elements = entry.target.querySelectorAll('.animate-on-scroll')
            elements.forEach((el, index) => {
              setTimeout(() => {
                el.classList.add('opacity-100', 'translate-y-0', 'translate-x-0')
                el.classList.remove('opacity-0', 'translate-y-8', 'translate-x-8')
              }, index * 150)
            })
          }
        })
      },
      { threshold: 0.1 }
    )

    const sections = document.querySelectorAll('section')
    sections.forEach(section => {
      if (section.querySelector('.animate-on-scroll')) {
        observer.observe(section)
      }
    })

    return () => observer.disconnect()
  }, [])

  // EMI Calculation Function
  const calculateEMI = () => {
    const principal = parseFloat(emiData.loanAmount)
    const rate = parseFloat(emiData.interestRate) / 12 / 100
    const time = parseFloat(emiData.tenure) * 12

    if (principal && rate && time) {
      const emi = (principal * rate * Math.pow(1 + rate, time)) / (Math.pow(1 + rate, time) - 1)
      const totalAmount = emi * time
      const totalInterest = totalAmount - principal

      setEmiResult({
        emi: Math.round(emi),
        totalAmount: Math.round(totalAmount),
        totalInterest: Math.round(totalInterest),
        principal: Math.round(principal)
      })
    }
  }

  // Rent vs Buy Calculation Function
  const calculateRentVsBuy = () => {
    const propertyPrice = parseFloat(rentBuyData.propertyPrice)
    const monthlyRent = parseFloat(rentBuyData.monthlyRent)
    const downPayment = parseFloat(rentBuyData.downPayment)
    const years = 10 // Analysis period

    if (propertyPrice && monthlyRent && downPayment) {
      const totalRentCost = monthlyRent * 12 * years * (1 + parseFloat(rentBuyData.rentIncrease || "5") / 100)
      const loanAmount = propertyPrice - downPayment
      const appreciatedValue = propertyPrice * Math.pow(1 + parseFloat(rentBuyData.propertyAppreciation || "8") / 100, years)
      
      setRentBuyResult({
        totalRentCost: Math.round(totalRentCost),
        totalBuyCost: Math.round(propertyPrice),
        appreciatedValue: Math.round(appreciatedValue),
        recommendation: totalRentCost < propertyPrice ? "rent" : "buy"
      })
    }
  }

  // ROI Calculation Function
  const calculateROI = () => {
    const propertyValue = parseFloat(roiData.propertyValue)
    const monthlyRental = parseFloat(roiData.monthlyRental)
    const maintenanceCost = parseFloat(roiData.maintenanceCost || "0")
    const propertyTax = parseFloat(roiData.propertyTax || "0")

    if (propertyValue && monthlyRental) {
      const annualRental = monthlyRental * 12
      const annualExpenses = maintenanceCost + propertyTax
      const netAnnualIncome = annualRental - annualExpenses
      const roi = (netAnnualIncome / propertyValue) * 100

      setRoiResult({
        annualRental: Math.round(annualRental),
        annualExpenses: Math.round(annualExpenses),
        netIncome: Math.round(netAnnualIncome),
        roi: roi.toFixed(2)
      })
    }
  }

  // Area Conversion Function
  const convertArea = () => {
    const value = parseFloat(areaData.value)
    if (!value) return

    const conversions: any = {
      sqft: { sqm: 0.092903, sqyd: 0.111111, acre: 0.*********, bigha: 0.000037, gaj: 0.111111, guntha: 0.000918 },
      sqm: { sqft: 10.7639, sqyd: 1.19599, acre: 0.*********, bigha: 0.000395, gaj: 1.19599, guntha: 0.00988 },
      sqyd: { sqft: 9, sqm: 0.836127, acre: 0.*********, bigha: 0.00033, gaj: 1, guntha: 0.00826 },
      acre: { sqft: 43560, sqm: 4046.86, sqyd: 4840, bigha: 1.613, gaj: 4840, guntha: 40 },
      bigha: { sqft: 27000, sqm: 2508.38, sqyd: 3000, acre: 0.619835, gaj: 3000, guntha: 24.79 },
      gaj: { sqft: 9, sqm: 0.836127, sqyd: 1, acre: 0.*********, bigha: 0.00033, guntha: 0.00826 },
      guntha: { sqft: 1089, sqm: 101.17, sqyd: 121, acre: 0.025, bigha: 0.0403, gaj: 121 }
    }

    const convertedValue = value * conversions[areaData.fromUnit][areaData.toUnit]

    setAreaResult({
      originalValue: value,
      convertedValue: convertedValue.toFixed(4),
      fromUnit: areaData.fromUnit,
      toUnit: areaData.toUnit
    })
  }

  // Loan Eligibility Calculation Function
  const calculateLoanEligibility = () => {
    const monthlyIncome = parseFloat(loanEligibilityData.monthlyIncome)
    const monthlyExpenses = parseFloat(loanEligibilityData.monthlyExpenses || "0")
    const existingEMI = parseFloat(loanEligibilityData.existingEMI || "0")
    const interestRate = parseFloat(loanEligibilityData.interestRate)
    const tenure = parseFloat(loanEligibilityData.tenure)
    const age = parseFloat(loanEligibilityData.age)

    if (monthlyIncome && interestRate && tenure && age) {
      const netIncome = monthlyIncome - monthlyExpenses - existingEMI
      const maxEMI = netIncome * 0.5 // 50% of net income as max EMI

      // Calculate maximum loan amount based on EMI capacity
      const rate = interestRate / 12 / 100
      const time = tenure * 12
      const maxLoanAmount = (maxEMI * (Math.pow(1 + rate, time) - 1)) / (rate * Math.pow(1 + rate, time))

      // Age factor (reduce loan amount for older applicants)
      const ageFactor = age <= 30 ? 1 : age <= 40 ? 0.9 : age <= 50 ? 0.8 : 0.7
      const adjustedLoanAmount = maxLoanAmount * ageFactor

      const eligibilityStatus = adjustedLoanAmount >= 500000 ? "eligible" : "not_eligible"

      setLoanEligibilityResult({
        maxLoanAmount: Math.round(adjustedLoanAmount),
        maxEMI: Math.round(maxEMI),
        netIncome: Math.round(netIncome),
        eligibilityStatus,
        ageFactor: (ageFactor * 100).toFixed(0)
      })
    }
  }

  const calculators = [
    {
      id: "emi",
      title: "EMI Calculator",
      description: "Calculate your home loan EMI",
      icon: Calculator,
      color: "from-blue-500 to-blue-600"
    },
    {
      id: "rentbuy",
      title: "Rent vs Buy",
      description: "Compare renting vs buying options",
      icon: Scale,
      color: "from-green-500 to-green-600"
    },
    {
      id: "roi",
      title: "ROI Calculator",
      description: "Calculate investment returns",
      icon: PieChart,
      color: "from-purple-500 to-purple-600"
    },
    {
      id: "area",
      title: "Area Conversion",
      description: "Convert between different area units",
      icon: Ruler,
      color: "from-orange-500 to-orange-600"
    },
    {
      id: "loan",
      title: "Loan Eligibility",
      description: "Check your loan eligibility",
      icon: CreditCard,
      color: "from-teal-500 to-teal-600"
    }
  ]

  return (
    <main className="min-h-screen">
      {/* Header Section */}
      <section className="relative pt-24 pb-16 lg:pt-28 lg:pb-20 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>
        
        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm">
              <Calculator size={16} className="mr-2" />
              Financial Tools
            </Badge>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              Real Estate <span className="text-yellow-300">Calculators</span>
            </h1>

            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Make informed property decisions with our comprehensive calculators. 
              Calculate EMI, compare rent vs buy, and analyze investment returns.
            </p>
          </div>
        </div>
      </section>

      {/* Calculator Selection */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
              <Target size={16} className="mr-2" />
              Choose Calculator
            </Badge>
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Select Your <span className="text-burgundy-600">Calculator</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Choose from our range of financial calculators to make smart property decisions.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {calculators.map((calc, index) => {
              const IconComponent = calc.icon
              return (
                <Card 
                  key={calc.id}
                  className={`animate-on-scroll border-0 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group ${
                    activeCalculator === calc.id ? 'ring-2 ring-burgundy-500 bg-burgundy-50' : 'hover:bg-gray-50'
                  }`}
                  style={{ animationDelay: `${index * 150}ms` }}
                  onClick={() => setActiveCalculator(calc.id)}
                >
                  <CardContent className="p-8 text-center">
                    <div className={`w-16 h-16 bg-gradient-to-r ${calc.color} rounded-full flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform`}>
                      <IconComponent className="w-8 h-8 text-white" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">{calc.title}</h3>
                    <p className="text-gray-600 leading-relaxed">{calc.description}</p>
                  </CardContent>
                </Card>
              )
            })}
          </div>
        </div>
      </section>

      {/* Calculator Content */}
      <section className="py-20 bg-textured-light">
        <div className="container mx-auto px-4">
          {/* EMI Calculator */}
          {activeCalculator === "emi" && (
            <div className="max-w-6xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-12">
                {/* Input Form */}
                <Card className="animate-on-scroll border-0 shadow-xl bg-textured-white">
                  <CardContent className="p-8">
                    <div className="mb-6">
                      <h3 className="text-3xl font-bold text-gray-900 mb-3">
                        EMI <span className="text-burgundy-600">Calculator</span>
                      </h3>
                      <p className="text-gray-600 text-lg">Calculate your monthly EMI for home loans</p>
                    </div>

                    <div className="space-y-6">
                      <CurrencyInput
                        value={emiData.loanAmount}
                        onChange={(e) => setEmiData({...emiData, loanAmount: e.target.value})}
                        placeholder="e.g., 5000000"
                        label="Loan Amount (₹)"
                      />

                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-700">Interest Rate (% per annum)</label>
                        <Input
                          type="number"
                          step="0.1"
                          value={emiData.interestRate}
                          onChange={(e) => setEmiData({...emiData, interestRate: e.target.value})}
                          placeholder="e.g., 8.5"
                          className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-700">Loan Tenure (Years)</label>
                        <Input
                          type="number"
                          value={emiData.tenure}
                          onChange={(e) => setEmiData({...emiData, tenure: e.target.value})}
                          placeholder="e.g., 20"
                          className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                        />
                      </div>

                      <Button
                        onClick={calculateEMI}
                        className="w-full bg-burgundy-600 hover:bg-burgundy-700 text-white h-12 rounded-xl font-semibold text-lg"
                      >
                        <Calculator size={20} className="mr-2" />
                        Calculate EMI
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Results */}
                <Card className="animate-on-scroll border-0 shadow-xl bg-textured-white">
                  <CardContent className="p-8">
                    <div className="mb-6">
                      <h3 className="text-3xl font-bold text-gray-900 mb-3">
                        EMI <span className="text-burgundy-600">Results</span>
                      </h3>
                      <p className="text-gray-600 text-lg">Your loan calculation breakdown</p>
                    </div>

                    {emiResult ? (
                      <div className="space-y-6">
                        <div className="grid grid-cols-2 gap-4">
                          <div className="bg-burgundy-50 p-6 rounded-xl text-center">
                            <IndianRupee className="w-8 h-8 text-burgundy-600 mx-auto mb-2" />
                            <p className="text-sm text-gray-600 mb-1">Monthly EMI</p>
                            <p className="text-2xl font-bold text-burgundy-600">{formatCurrencyWithUnit(emiResult.emi)}</p>
                          </div>
                          <div className="bg-blue-50 p-6 rounded-xl text-center">
                            <DollarSign className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                            <p className="text-sm text-gray-600 mb-1">Total Amount</p>
                            <p className="text-2xl font-bold text-blue-600">{formatCurrencyWithUnit(emiResult.totalAmount)}</p>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div className="bg-green-50 p-6 rounded-xl text-center">
                            <Home className="w-8 h-8 text-green-600 mx-auto mb-2" />
                            <p className="text-sm text-gray-600 mb-1">Principal Amount</p>
                            <p className="text-2xl font-bold text-green-600">{formatCurrencyWithUnit(emiResult.principal)}</p>
                          </div>
                          <div className="bg-orange-50 p-6 rounded-xl text-center">
                            <Percent className="w-8 h-8 text-orange-600 mx-auto mb-2" />
                            <p className="text-sm text-gray-600 mb-1">Total Interest</p>
                            <p className="text-2xl font-bold text-orange-600">{formatCurrencyWithUnit(emiResult.totalInterest)}</p>
                          </div>
                        </div>

                        <div className="bg-gray-50 p-6 rounded-xl">
                          <h4 className="font-semibold text-gray-900 mb-3">Loan Summary</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Loan Amount:</span>
                              <span className="font-semibold">{formatCurrencyWithUnit(emiResult.principal)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Total Interest:</span>
                              <span className="font-semibold">{formatCurrencyWithUnit(emiResult.totalInterest)}</span>
                            </div>
                            <div className="flex justify-between border-t pt-2">
                              <span className="text-gray-900 font-semibold">Total Payable:</span>
                              <span className="font-bold text-burgundy-600">{formatCurrencyWithUnit(emiResult.totalAmount)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <Calculator className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500">Enter loan details to calculate EMI</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Rent vs Buy Calculator */}
          {activeCalculator === "rentbuy" && (
            <div className="max-w-6xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-12">
                {/* Input Form */}
                <Card className="animate-on-scroll border-0 shadow-xl bg-textured-white">
                  <CardContent className="p-8">
                    <div className="mb-6">
                      <h3 className="text-3xl font-bold text-gray-900 mb-3">
                        Rent vs <span className="text-burgundy-600">Buy</span>
                      </h3>
                      <p className="text-gray-600 text-lg">Compare renting vs buying a property</p>
                    </div>

                    <div className="space-y-6">
                      <CurrencyInput
                        value={rentBuyData.propertyPrice}
                        onChange={(e) => setRentBuyData({...rentBuyData, propertyPrice: e.target.value})}
                        placeholder="e.g., 5000000"
                        label="Property Price (₹)"
                      />

                      <CurrencyInput
                        value={rentBuyData.monthlyRent}
                        onChange={(e) => setRentBuyData({...rentBuyData, monthlyRent: e.target.value})}
                        placeholder="e.g., 25000"
                        label="Monthly Rent (₹)"
                      />

                      <CurrencyInput
                        value={rentBuyData.downPayment}
                        onChange={(e) => setRentBuyData({...rentBuyData, downPayment: e.target.value})}
                        placeholder="e.g., 1000000"
                        label="Down Payment (₹)"
                      />

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <label className="block text-sm font-semibold text-gray-700">Property Appreciation (%)</label>
                          <Input
                            type="number"
                            step="0.1"
                            value={rentBuyData.propertyAppreciation}
                            onChange={(e) => setRentBuyData({...rentBuyData, propertyAppreciation: e.target.value})}
                            placeholder="8"
                            className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                          />
                        </div>
                        <div className="space-y-2">
                          <label className="block text-sm font-semibold text-gray-700">Rent Increase (%)</label>
                          <Input
                            type="number"
                            step="0.1"
                            value={rentBuyData.rentIncrease}
                            onChange={(e) => setRentBuyData({...rentBuyData, rentIncrease: e.target.value})}
                            placeholder="5"
                            className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                          />
                        </div>
                      </div>

                      <Button
                        onClick={calculateRentVsBuy}
                        className="w-full bg-burgundy-600 hover:bg-burgundy-700 text-white h-12 rounded-xl font-semibold text-lg"
                      >
                        <Scale size={20} className="mr-2" />
                        Compare Options
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Results */}
                <Card className="animate-on-scroll border-0 shadow-xl bg-textured-white">
                  <CardContent className="p-8">
                    <div className="mb-6">
                      <h3 className="text-3xl font-bold text-gray-900 mb-3">
                        Comparison <span className="text-burgundy-600">Results</span>
                      </h3>
                      <p className="text-gray-600 text-lg">10-year analysis breakdown</p>
                    </div>

                    {rentBuyResult ? (
                      <div className="space-y-6">
                        <div className="grid grid-cols-1 gap-4">
                          <div className="bg-blue-50 p-6 rounded-xl">
                            <div className="flex items-center justify-between mb-4">
                              <h4 className="font-semibold text-blue-900">Renting Option</h4>
                              <Home className="w-6 h-6 text-blue-600" />
                            </div>
                            <p className="text-3xl font-bold text-blue-600 mb-2">{formatCurrencyWithUnit(rentBuyResult.totalRentCost)}</p>
                            <p className="text-sm text-blue-700">Total cost over 10 years</p>
                          </div>

                          <div className="bg-green-50 p-6 rounded-xl">
                            <div className="flex items-center justify-between mb-4">
                              <h4 className="font-semibold text-green-900">Buying Option</h4>
                              <TrendingUp className="w-6 h-6 text-green-600" />
                            </div>
                            <p className="text-3xl font-bold text-green-600 mb-2">{formatCurrencyWithUnit(rentBuyResult.appreciatedValue)}</p>
                            <p className="text-sm text-green-700">Property value after 10 years</p>
                          </div>
                        </div>

                        <div className={`p-6 rounded-xl ${rentBuyResult.recommendation === 'buy' ? 'bg-green-50 border-2 border-green-200' : 'bg-blue-50 border-2 border-blue-200'}`}>
                          <div className="flex items-center mb-4">
                            <Target className={`w-6 h-6 mr-3 ${rentBuyResult.recommendation === 'buy' ? 'text-green-600' : 'text-blue-600'}`} />
                            <h4 className={`font-bold text-lg ${rentBuyResult.recommendation === 'buy' ? 'text-green-900' : 'text-blue-900'}`}>
                              Recommendation: {rentBuyResult.recommendation === 'buy' ? 'BUY' : 'RENT'}
                            </h4>
                          </div>
                          <p className={`text-sm ${rentBuyResult.recommendation === 'buy' ? 'text-green-700' : 'text-blue-700'}`}>
                            {rentBuyResult.recommendation === 'buy'
                              ? 'Buying is more beneficial in the long term considering property appreciation.'
                              : 'Renting is more cost-effective based on current market conditions.'
                            }
                          </p>
                        </div>

                        <div className="bg-gray-50 p-6 rounded-xl">
                          <h4 className="font-semibold text-gray-900 mb-3">Analysis Summary</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Property Purchase Price:</span>
                              <span className="font-semibold">{formatCurrencyWithUnit(parseInt(rentBuyData.propertyPrice))}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Total Rent (10 years):</span>
                              <span className="font-semibold">{formatCurrencyWithUnit(rentBuyResult.totalRentCost)}</span>
                            </div>
                            <div className="flex justify-between border-t pt-2">
                              <span className="text-gray-900 font-semibold">Property Value (10 years):</span>
                              <span className="font-bold text-burgundy-600">{formatCurrencyWithUnit(rentBuyResult.appreciatedValue)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <Scale className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500">Enter property details to compare options</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* ROI Calculator */}
          {activeCalculator === "roi" && (
            <div className="max-w-6xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-12">
                {/* Input Form */}
                <Card className="animate-on-scroll border-0 shadow-xl bg-textured-white">
                  <CardContent className="p-8">
                    <div className="mb-6">
                      <h3 className="text-3xl font-bold text-gray-900 mb-3">
                        ROI <span className="text-burgundy-600">Calculator</span>
                      </h3>
                      <p className="text-gray-600 text-lg">Calculate return on investment for rental properties</p>
                    </div>

                    <div className="space-y-6">
                      <CurrencyInput
                        value={roiData.propertyValue}
                        onChange={(e) => setRoiData({...roiData, propertyValue: e.target.value})}
                        placeholder="e.g., 5000000"
                        label="Property Value (₹)"
                      />

                      <CurrencyInput
                        value={roiData.monthlyRental}
                        onChange={(e) => setRoiData({...roiData, monthlyRental: e.target.value})}
                        placeholder="e.g., 25000"
                        label="Monthly Rental Income (₹)"
                      />

                      <CurrencyInput
                        value={roiData.maintenanceCost}
                        onChange={(e) => setRoiData({...roiData, maintenanceCost: e.target.value})}
                        placeholder="e.g., 50000"
                        label="Annual Maintenance Cost (₹)"
                      />

                      <CurrencyInput
                        value={roiData.propertyTax}
                        onChange={(e) => setRoiData({...roiData, propertyTax: e.target.value})}
                        placeholder="e.g., 25000"
                        label="Annual Property Tax (₹)"
                      />

                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-700">Expected Appreciation Rate (% per annum)</label>
                        <Input
                          type="number"
                          step="0.1"
                          value={roiData.appreciationRate}
                          onChange={(e) => setRoiData({...roiData, appreciationRate: e.target.value})}
                          placeholder="e.g., 8"
                          className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                        />
                      </div>

                      <Button
                        onClick={calculateROI}
                        className="w-full bg-burgundy-600 hover:bg-burgundy-700 text-white h-12 rounded-xl font-semibold text-lg"
                      >
                        <PieChart size={20} className="mr-2" />
                        Calculate ROI
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Results */}
                <Card className="animate-on-scroll border-0 shadow-xl bg-textured-white">
                  <CardContent className="p-8">
                    <div className="mb-6">
                      <h3 className="text-3xl font-bold text-gray-900 mb-3">
                        ROI <span className="text-burgundy-600">Analysis</span>
                      </h3>
                      <p className="text-gray-600 text-lg">Your investment return breakdown</p>
                    </div>

                    {roiResult ? (
                      <div className="space-y-6">
                        <div className="bg-burgundy-50 p-6 rounded-xl text-center border-2 border-burgundy-200">
                          <BarChart3 className="w-12 h-12 text-burgundy-600 mx-auto mb-4" />
                          <p className="text-sm text-gray-600 mb-2">Annual ROI</p>
                          <p className="text-4xl font-bold text-burgundy-600 mb-2">{roiResult.roi}%</p>
                          <p className="text-sm text-burgundy-700">Return on Investment</p>
                        </div>

                        <div className="grid grid-cols-1 gap-4">
                          <div className="bg-green-50 p-6 rounded-xl">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-gray-600">Annual Rental Income</span>
                              <TrendingUp className="w-5 h-5 text-green-600" />
                            </div>
                            <p className="text-2xl font-bold text-green-600">{formatCurrencyWithUnit(roiResult.annualRental)}</p>
                          </div>

                          <div className="bg-red-50 p-6 rounded-xl">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-gray-600">Annual Expenses</span>
                              <ArrowRight className="w-5 h-5 text-red-600" />
                            </div>
                            <p className="text-2xl font-bold text-red-600">{formatCurrencyWithUnit(roiResult.annualExpenses)}</p>
                          </div>

                          <div className="bg-blue-50 p-6 rounded-xl">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-gray-600">Net Annual Income</span>
                              <DollarSign className="w-5 h-5 text-blue-600" />
                            </div>
                            <p className="text-2xl font-bold text-blue-600">{formatCurrencyWithUnit(roiResult.netIncome)}</p>
                          </div>
                        </div>

                        <div className="bg-gray-50 p-6 rounded-xl">
                          <h4 className="font-semibold text-gray-900 mb-3">Investment Summary</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Property Investment:</span>
                              <span className="font-semibold">{formatCurrencyWithUnit(parseInt(roiData.propertyValue))}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Monthly Rental:</span>
                              <span className="font-semibold">{formatCurrencyWithUnit(parseInt(roiData.monthlyRental))}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Annual Net Income:</span>
                              <span className="font-semibold">{formatCurrencyWithUnit(roiResult.netIncome)}</span>
                            </div>
                            <div className="flex justify-between border-t pt-2">
                              <span className="text-gray-900 font-semibold">ROI Percentage:</span>
                              <span className="font-bold text-burgundy-600">{roiResult.roi}%</span>
                            </div>
                          </div>
                        </div>

                        <div className={`p-4 rounded-xl ${parseFloat(roiResult.roi) >= 8 ? 'bg-green-50 border border-green-200' : parseFloat(roiResult.roi) >= 5 ? 'bg-yellow-50 border border-yellow-200' : 'bg-red-50 border border-red-200'}`}>
                          <p className={`text-sm font-medium ${parseFloat(roiResult.roi) >= 8 ? 'text-green-800' : parseFloat(roiResult.roi) >= 5 ? 'text-yellow-800' : 'text-red-800'}`}>
                            {parseFloat(roiResult.roi) >= 8 ? '✅ Excellent ROI - Great investment opportunity!' :
                             parseFloat(roiResult.roi) >= 5 ? '⚠️ Moderate ROI - Consider market conditions' :
                             '❌ Low ROI - May need better rental yield or lower purchase price'}
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <PieChart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500">Enter property details to calculate ROI</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}

          {/* Area Conversion Calculator */}
          {activeCalculator === "area" && (
            <div className="max-w-4xl mx-auto">
              <Card className="animate-on-scroll border-0 shadow-xl bg-textured-white">
                <CardContent className="p-8">
                  <div className="mb-6">
                    <h3 className="text-3xl font-bold text-gray-900 mb-3">
                      Area <span className="text-burgundy-600">Conversion</span>
                    </h3>
                    <p className="text-gray-600 text-lg">Convert between different area units easily</p>
                  </div>

                  <div className="grid md:grid-cols-3 gap-6 mb-8">
                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">Area Value</label>
                      <Input
                        type="number"
                        value={areaData.value}
                        onChange={(e) => setAreaData({...areaData, value: e.target.value})}
                        placeholder="Enter area value"
                        className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                      />
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">From Unit</label>
                      <select
                        value={areaData.fromUnit}
                        onChange={(e) => setAreaData({...areaData, fromUnit: e.target.value})}
                        className="w-full h-12 px-3 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                      >
                        <option value="sqft">Square Feet (sq ft)</option>
                        <option value="sqm">Square Meter (sq m)</option>
                        <option value="sqyd">Square Yard (sq yd)</option>
                        <option value="acre">Acre</option>
                        <option value="bigha">Bigha</option>
                        <option value="gaj">Gaj</option>
                        <option value="guntha">Guntha</option>
                      </select>
                    </div>

                    <div className="space-y-2">
                      <label className="block text-sm font-semibold text-gray-700">To Unit</label>
                      <select
                        value={areaData.toUnit}
                        onChange={(e) => setAreaData({...areaData, toUnit: e.target.value})}
                        className="w-full h-12 px-3 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                      >
                        <option value="sqft">Square Feet (sq ft)</option>
                        <option value="sqm">Square Meter (sq m)</option>
                        <option value="sqyd">Square Yard (sq yd)</option>
                        <option value="acre">Acre</option>
                        <option value="bigha">Bigha</option>
                        <option value="gaj">Gaj</option>
                        <option value="guntha">Guntha</option>
                      </select>
                    </div>
                  </div>

                  <Button
                    onClick={convertArea}
                    className="w-full h-12 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white font-semibold rounded-xl transition-all duration-300"
                  >
                    <Ruler className="w-5 h-5 mr-2" />
                    Convert Area
                  </Button>

                  {areaResult && (
                    <div className="mt-8 p-6 bg-gradient-to-r from-orange-50 to-orange-100 rounded-xl border border-orange-200">
                      <h4 className="font-semibold text-gray-900 mb-4 text-center">Conversion Result</h4>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">
                          <span className="text-orange-600">{areaResult.originalValue}</span>
                          <span className="text-gray-600 mx-3">{areaResult.fromUnit}</span>
                          <span className="text-gray-400 mx-3">=</span>
                          <span className="text-orange-600">{areaResult.convertedValue}</span>
                          <span className="text-gray-600 mx-3">{areaResult.toUnit}</span>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Loan Eligibility Calculator */}
          {activeCalculator === "loan" && (
            <div className="max-w-6xl mx-auto">
              <div className="grid lg:grid-cols-2 gap-12">
                {/* Input Form */}
                <Card className="animate-on-scroll border-0 shadow-xl bg-textured-white">
                  <CardContent className="p-8">
                    <div className="mb-6">
                      <h3 className="text-3xl font-bold text-gray-900 mb-3">
                        Loan <span className="text-burgundy-600">Eligibility</span>
                      </h3>
                      <p className="text-gray-600 text-lg">Check your home loan eligibility</p>
                    </div>

                    <div className="space-y-6">
                      <CurrencyInput
                        value={loanEligibilityData.monthlyIncome}
                        onChange={(e) => setLoanEligibilityData({...loanEligibilityData, monthlyIncome: e.target.value})}
                        placeholder="e.g., 100000"
                        label="Monthly Income (₹)"
                      />

                      <CurrencyInput
                        value={loanEligibilityData.monthlyExpenses}
                        onChange={(e) => setLoanEligibilityData({...loanEligibilityData, monthlyExpenses: e.target.value})}
                        placeholder="e.g., 30000"
                        label="Monthly Expenses (₹)"
                      />

                      <CurrencyInput
                        value={loanEligibilityData.existingEMI}
                        onChange={(e) => setLoanEligibilityData({...loanEligibilityData, existingEMI: e.target.value})}
                        placeholder="e.g., 15000"
                        label="Existing EMI (₹)"
                      />

                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-700">Interest Rate (% per annum)</label>
                        <Input
                          type="number"
                          step="0.1"
                          value={loanEligibilityData.interestRate}
                          onChange={(e) => setLoanEligibilityData({...loanEligibilityData, interestRate: e.target.value})}
                          placeholder="e.g., 8.5"
                          className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-700">Loan Tenure (Years)</label>
                        <Input
                          type="number"
                          value={loanEligibilityData.tenure}
                          onChange={(e) => setLoanEligibilityData({...loanEligibilityData, tenure: e.target.value})}
                          placeholder="e.g., 20"
                          className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="block text-sm font-semibold text-gray-700">Age (Years)</label>
                        <Input
                          type="number"
                          value={loanEligibilityData.age}
                          onChange={(e) => setLoanEligibilityData({...loanEligibilityData, age: e.target.value})}
                          placeholder="e.g., 35"
                          className="h-12 rounded-xl border border-gray-200 focus:border-2 focus:border-burgundy-500 focus:ring-0"
                        />
                      </div>

                      <Button
                        onClick={calculateLoanEligibility}
                        className="w-full h-12 bg-gradient-to-r from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700 text-white font-semibold rounded-xl transition-all duration-300"
                      >
                        <CreditCard className="w-5 h-5 mr-2" />
                        Check Eligibility
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Results */}
                <Card className="animate-on-scroll border-0 shadow-xl bg-textured-white">
                  <CardContent className="p-8">
                    <h4 className="text-2xl font-bold text-gray-900 mb-6">Eligibility Results</h4>

                    {loanEligibilityResult ? (
                      <div className="space-y-6">
                        <div className={`p-6 rounded-xl text-center ${loanEligibilityResult.eligibilityStatus === 'eligible' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'}`}>
                          <div className={`text-3xl font-bold mb-2 ${loanEligibilityResult.eligibilityStatus === 'eligible' ? 'text-green-600' : 'text-red-600'}`}>
                            {loanEligibilityResult.eligibilityStatus === 'eligible' ? '✅ Eligible' : '❌ Not Eligible'}
                          </div>
                          <p className={`text-sm ${loanEligibilityResult.eligibilityStatus === 'eligible' ? 'text-green-700' : 'text-red-700'}`}>
                            {loanEligibilityResult.eligibilityStatus === 'eligible'
                              ? 'You are eligible for a home loan!'
                              : 'You may need to improve your financial profile'}
                          </p>
                        </div>

                        <div className="bg-gray-50 p-6 rounded-xl">
                          <h4 className="font-semibold text-gray-900 mb-3">Loan Details</h4>
                          <div className="space-y-2 text-sm">
                            <div className="flex justify-between">
                              <span className="text-gray-600">Maximum Loan Amount:</span>
                              <span className="font-semibold">{formatCurrencyWithUnit(loanEligibilityResult.maxLoanAmount)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Maximum EMI:</span>
                              <span className="font-semibold">{formatCurrencyWithUnit(loanEligibilityResult.maxEMI)}</span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-gray-600">Net Monthly Income:</span>
                              <span className="font-semibold">{formatCurrencyWithUnit(loanEligibilityResult.netIncome)}</span>
                            </div>
                            <div className="flex justify-between border-t pt-2">
                              <span className="text-gray-900 font-semibold">Age Factor:</span>
                              <span className="font-bold text-burgundy-600">{loanEligibilityResult.ageFactor}%</span>
                            </div>
                          </div>
                        </div>

                        <div className="bg-blue-50 p-4 rounded-xl border border-blue-200">
                          <p className="text-sm text-blue-800">
                            <strong>Note:</strong> This is an indicative calculation. Actual loan approval depends on various factors including credit score, employment history, and bank policies.
                          </p>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <CreditCard className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                        <p className="text-gray-500">Enter your details to check loan eligibility</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            </div>
          )}
        </div>
      </section>
    </main>
  )
}
