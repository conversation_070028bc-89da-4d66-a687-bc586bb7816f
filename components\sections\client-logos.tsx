"use client"

import { useEffect, useRef } from "react"
import { LogoCarousel } from "@/components/ui/logo-carousel"

const clients = [
  { id: 1, name: "Godrej Properties", src: "/images/developers/godrej.webp" },
  { id: 2, name: "Sobha Limited", src: "/images/developers/sobha.webp" },
  { id: 3, name: "Lodha Group", src: "/images/developers/lodha.webp" },
  { id: 4, name: "Aaryan Group", src: "/images/developers/aaryan.webp" },
  { id: 5, name: "Aaryan Properties", src: "/images/developers/aaryan-2.webp" },
  { id: 6, name: "Gala Group", src: "/images/developers/gald.webp" },
  { id: 7, name: "HRG Group", src: "/images/developers/hrg.webp" },
  { id: 8, name: "Safal Group", src: "/images/developers/safal.webp" },
  { id: 9, name: "Savaliya Group", src: "/images/developers/savaliya.webp" },
  { id: 10, name: "Shaligram Group", src: "/images/developers/shaligram.webp" },
  { id: 11, name: "Shipl Group", src: "/images/developers/shipl.webp" },
  { id: 12, name: "Swagat Group", src: "/images/developers/swagat.webp" },
  { id: 13, name: "Swati Group", src: "/images/developers/swati.webp" },
  { id: 14, name: "Arista Homes", src: "/images/developers/arista.webp" },
  { id: 15, name: "Constera Group", src: "/images/developers/constera.webp" },
  { id: 16, name: "Deep Group", src: "/images/developers/deep.webp" },
  { id: 17, name: "E Square", src: "/images/developers/e.webp" },
  { id: 18, name: "HR Group", src: "/images/developers/hr.webp" },
  { id: 19, name: "Pacific Group", src: "/images/developers/pacific.webp" },
  { id: 20, name: "PD Group", src: "/images/developers/pd.webp" },
  { id: 21, name: "Ratnanjali Group", src: "/images/developers/ratnanjali.webp" },
  { id: 22, name: "Venus Group", src: "/images/developers/venus.webp" },
  { id: 23, name: "Zaveri Group", src: "/images/developers/zaveri.webp" },
]

export default function ClientLogos() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-br from-burgundy-50 via-white to-burgundy-50 relative overflow-hidden">
      {/* Animated Particles Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-10 left-10 w-2 h-2 bg-burgundy-200 rounded-full animate-bounce-minimal opacity-60"></div>
        <div className="absolute top-20 right-20 w-1 h-1 bg-burgundy-300 rounded-full animate-pulse-gentle opacity-40"></div>
        <div className="absolute bottom-32 left-16 w-3 h-3 bg-burgundy-100 rounded-full animate-float-minimal opacity-50"></div>
        <div className="absolute bottom-20 right-32 w-2 h-2 bg-burgundy-200 rounded-full animate-bounce-minimal opacity-30"></div>
        <div className="absolute top-1/2 left-1/4 w-1 h-1 bg-burgundy-300 rounded-full animate-pulse-gentle opacity-60"></div>
        <div className="absolute top-1/3 right-1/3 w-2 h-2 bg-burgundy-100 rounded-full animate-float-minimal opacity-40"></div>
        <div className="absolute bottom-1/3 left-1/2 w-1 h-1 bg-burgundy-200 rounded-full animate-bounce-minimal opacity-50"></div>
        <div className="absolute top-3/4 right-1/4 w-3 h-3 bg-burgundy-100 rounded-full animate-pulse-gentle opacity-30"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center space-y-4 mb-12 animate-on-scroll opacity-0 translate-y-8">
          <p className="text-sm font-medium tracking-widest text-burgundy-600 uppercase">
            Trusted by teams from around the world
          </p>
          <h2 className="text-4xl lg:text-5xl xl:text-6xl font-bold tracking-tight leading-none text-gray-900">
            The best are{" "}
            <span className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 bg-clip-text text-transparent">
              already here
            </span>
          </h2>
        </div>

        <div className="animate-on-scroll opacity-0 translate-y-8 delay-200">
          <LogoCarousel logos={clients} columns={5} />
        </div>
      </div>
    </section>
  )
}
