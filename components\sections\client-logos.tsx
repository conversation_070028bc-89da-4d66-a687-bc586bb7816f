"use client"

import { useEffect, useRef } from "react"
import Image from "next/image"

const clients = [
  { name: "Godrej Properties", logo: "/images/developers/godrej.webp" },
  { name: "Sobha Limited", logo: "/images/developers/sobha.webp" },
  { name: "Lodha Group", logo: "/images/developers/lodha.webp" },
  { name: "Aaryan Group", logo: "/images/developers/aaryan.webp" },
  { name: "Aaryan Properties", logo: "/images/developers/aaryan-2.webp" },
  { name: "Gala Group", logo: "/images/developers/gald.webp" },
  { name: "HRG Group", logo: "/images/developers/hrg.webp" },
  { name: "Safal Group", logo: "/images/developers/safal.webp" },
  { name: "Savaliya Group", logo: "/images/developers/savaliya.webp" },
  { name: "Shaligram Group", logo: "/images/developers/shaligram.webp" },
  { name: "Shipl Group", logo: "/images/developers/shipl.webp" },
  { name: "Swagat Group", logo: "/images/developers/swagat.webp" },
  { name: "Swati Group", logo: "/images/developers/swati.webp" },
  { name: "Arista Homes", logo: "/images/developers/arista.webp" },
  { name: "Constera Group", logo: "/images/developers/constera.webp" },
  { name: "Deep Group", logo: "/images/developers/deep.webp" },
  { name: "E Square", logo: "/images/developers/e.webp" },
  { name: "HR Group", logo: "/images/developers/hr.webp" },
  { name: "Pacific Group", logo: "/images/developers/pacific.webp" },
  { name: "PD Group", logo: "/images/developers/pd.webp" },
  { name: "Ratnanjali Group", logo: "/images/developers/ratnanjali.webp" },
  { name: "Venus Group", logo: "/images/developers/venus.webp" },
  { name: "Zaveri Group", logo: "/images/developers/zaveri.webp" },
]

export default function ClientLogos() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-16 bg-gradient-to-br from-gray-50 to-gray-100">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 animate-on-scroll opacity-0 translate-y-8">
          <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
            Our Valued{" "}
            <span className="bg-gradient-to-r from-blue-600 to-blue-700 bg-clip-text text-transparent">
              Clients
            </span>
          </h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            We have successfully served numerous clients with our premium real estate services and
            transparent dealings across Ahmedabad.
          </p>
        </div>

        <div className="animate-on-scroll opacity-0 translate-y-8 delay-200">
          {/* Grid Layout for Clients */}
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center">
            {clients.slice(0, 12).map((client, index) => (
              <div
                key={index}
                className="flex items-center justify-center p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 group"
              >
                <Image
                  src={client.logo || "/placeholder.svg"}
                  alt={client.name}
                  width={120}
                  height={60}
                  className="h-12 w-auto object-contain grayscale group-hover:grayscale-0 transition-all duration-300"
                />
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
