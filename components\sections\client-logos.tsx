"use client"

import { useEffect, useRef } from "react"
import Image from "next/image"

const clients = [
  { name: "Godrej Properties", logo: "/images/developers/godrej.webp" },
  { name: "Sobha Limited", logo: "/images/developers/sobha.webp" },
  { name: "Lodha Group", logo: "/images/developers/lodha.webp" },
  { name: "Aaryan Group", logo: "/images/developers/aaryan.webp" },
  { name: "Aaryan Properties", logo: "/images/developers/aaryan-2.webp" },
  { name: "Gala Group", logo: "/images/developers/gald.webp" },
  { name: "HRG Group", logo: "/images/developers/hrg.webp" },
  { name: "Safal Group", logo: "/images/developers/safal.webp" },
  { name: "Savaliya Group", logo: "/images/developers/savaliya.webp" },
  { name: "Shaligram Group", logo: "/images/developers/shaligram.webp" },
  { name: "Shipl Group", logo: "/images/developers/shipl.webp" },
  { name: "Swagat Group", logo: "/images/developers/swagat.webp" },
  { name: "Swati Group", logo: "/images/developers/swati.webp" },
  { name: "Arista Homes", logo: "/images/developers/arista.webp" },
  { name: "Constera Group", logo: "/images/developers/constera.webp" },
  { name: "Deep Group", logo: "/images/developers/deep.webp" },
  { name: "E Square", logo: "/images/developers/e.webp" },
  { name: "HR Group", logo: "/images/developers/hr.webp" },
  { name: "Pacific Group", logo: "/images/developers/pacific.webp" },
  { name: "PD Group", logo: "/images/developers/pd.webp" },
  { name: "Ratnanjali Group", logo: "/images/developers/ratnanjali.webp" },
  { name: "Venus Group", logo: "/images/developers/venus.webp" },
  { name: "Zaveri Group", logo: "/images/developers/zaveri.webp" },
]

export default function ClientLogos() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-20 bg-gradient-to-br from-burgundy-50 via-white to-burgundy-50 relative overflow-hidden">
      {/* Animated Particles Background */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-10 left-10 w-2 h-2 bg-burgundy-200 rounded-full animate-bounce-minimal opacity-60"></div>
        <div className="absolute top-20 right-20 w-1 h-1 bg-burgundy-300 rounded-full animate-pulse-gentle opacity-40"></div>
        <div className="absolute bottom-32 left-16 w-3 h-3 bg-burgundy-100 rounded-full animate-float-minimal opacity-50"></div>
        <div className="absolute bottom-20 right-32 w-2 h-2 bg-burgundy-200 rounded-full animate-bounce-minimal opacity-30"></div>
        <div className="absolute top-1/2 left-1/4 w-1 h-1 bg-burgundy-300 rounded-full animate-pulse-gentle opacity-60"></div>
        <div className="absolute top-1/3 right-1/3 w-2 h-2 bg-burgundy-100 rounded-full animate-float-minimal opacity-40"></div>
        <div className="absolute bottom-1/3 left-1/2 w-1 h-1 bg-burgundy-200 rounded-full animate-bounce-minimal opacity-50"></div>
        <div className="absolute top-3/4 right-1/4 w-3 h-3 bg-burgundy-100 rounded-full animate-pulse-gentle opacity-30"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16 animate-on-scroll opacity-0 translate-y-8">
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            Trusted by experts.
          </h2>
          <h3 className="text-2xl lg:text-3xl font-bold mb-6">
            <span className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 bg-clip-text text-transparent">
              Used by the leaders.
            </span>
          </h3>
        </div>

        <div className="animate-on-scroll opacity-0 translate-y-8 delay-200">
          {/* Scrolling Logo Animation */}
          <div className="relative overflow-hidden group">
            <div className="flex animate-scroll space-x-16 items-center group-hover:pause" style={{minWidth: 'max-content'}}>
              {/* First set of logos */}
              {clients.map((client, index) => (
                <div
                  key={`first-${index}`}
                  className="flex-shrink-0 transition-all duration-300 opacity-70 hover:opacity-100 filter grayscale hover:grayscale-0"
                >
                  <Image
                    src={client.logo || "/placeholder.svg"}
                    alt={client.name}
                    width={140}
                    height={70}
                    className="h-16 w-auto object-contain"
                  />
                </div>
              ))}
              {/* Duplicate set for seamless loop */}
              {clients.map((client, index) => (
                <div
                  key={`second-${index}`}
                  className="flex-shrink-0 transition-all duration-300 opacity-70 hover:opacity-100 filter grayscale hover:grayscale-0"
                >
                  <Image
                    src={client.logo || "/placeholder.svg"}
                    alt={client.name}
                    width={140}
                    height={70}
                    className="h-16 w-auto object-contain"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
