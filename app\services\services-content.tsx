"use client"

import { useEffect, useRef } from "react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import {
  Home,
  Building2,
  Key,
  Megaphone,
  Palette,
  Users,
  CheckCircle,
  ArrowRight,
  Phone,
  Mail,
  MapPin,
  Star,
  TrendingUp,
  Shield,
  Award,
  Handshake,
  Target,
  Briefcase,
  Settings
} from "lucide-react"
import Link from "next/link"

const mainServices = [
  {
    icon: Home,
    title: "Residential Property Buying & Selling",
    description: "Complete solutions for all your residential property needs with expert guidance and market insights.",
    features: [
      "Flats in premium locations across Ahmedabad",
      "Independent villas and luxury homes",
      "Residential plots for custom construction",
      "Premium homes with modern amenities",
      "Resale property evaluation and pricing",
      "Legal documentation and registration support"
    ],
    benefits: [
      "Access to 500+ verified residential properties",
      "Expert market analysis and pricing guidance",
      "End-to-end transaction support",
      "Post-sale assistance and documentation"
    ],
    color: "from-blue-600 to-blue-700"
  },
  {
    icon: Building2,
    title: "Commercial Property Leasing & Sales",
    description: "Strategic commercial real estate solutions for businesses looking to establish or expand their presence.",
    features: [
      "Prime office spaces in business districts",
      "Retail showrooms in high-footfall areas",
      "Warehouses and industrial spaces",
      "Co-working and flexible office solutions",
      "Commercial plot sales for development",
      "Investment-grade commercial properties"
    ],
    benefits: [
      "Strategic location analysis for business growth",
      "Flexible leasing terms and conditions",
      "ROI-focused investment opportunities",
      "Comprehensive market research and insights"
    ],
    color: "from-green-600 to-green-700"
  },
  {
    icon: Megaphone,
    title: "Project Marketing for Builders & Developers",
    description: "Comprehensive marketing solutions to maximize project visibility and accelerate sales for real estate developers.",
    features: [
      "End-to-end branding and positioning strategy",
      "Digital marketing and lead generation campaigns",
      "Sales execution and customer relationship management",
      "Pre-launch offers and strategic market positioning",
      "Channel partner network development",
      "Market research and competitive analysis"
    ],
    benefits: [
      "Proven track record with 50+ successful projects",
      "Multi-channel marketing approach",
      "Dedicated sales team and CRM systems",
      "Performance-driven results and analytics"
    ],
    color: "from-purple-600 to-purple-700"
  }
]

export default function ServicesContent() {
  const mainRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px"
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.remove("opacity-0", "translate-y-8")
          entry.target.classList.add("opacity-100", "translate-y-0")
        }
      })
    }, observerOptions)

    const animatedElements = mainRef.current?.querySelectorAll(".animate-on-scroll")
    animatedElements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  // Handle anchor scrolling on page load
  useEffect(() => {
    const hash = window.location.hash
    if (hash) {
      setTimeout(() => {
        const element = document.querySelector(hash)
        if (element) {
          const offset = 100 // Account for fixed navbar
          const elementPosition = element.getBoundingClientRect().top
          const offsetPosition = elementPosition + window.pageYOffset - offset

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          })
        }
      }, 500) // Small delay to ensure page is fully loaded
    }
  }, [])

  return (
    <main ref={mainRef} className="min-h-screen">
      {/* Header Section */}
      <section className="relative pt-24 pb-16 lg:pt-28 lg:pb-20 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>

        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm">
              <Settings size={16} className="mr-2" />
              Our Services
            </Badge>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              Comprehensive <span className="text-yellow-300">Real Estate</span> Solutions
            </h1>

            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              From residential properties to commercial spaces, project marketing to specialized consultations - we offer complete real estate solutions tailored to your needs.
            </p>
          </div>
        </div>
      </section>

      {/* Main Services Section */}
      <section id="main-services" className="py-20 bg-textured-light">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <div className="text-center mb-16">
                <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
                  <Briefcase size={16} className="mr-2" />
                  Core Services
                </Badge>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                  Our <span className="text-burgundy-600">Primary</span> Offerings
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Comprehensive real estate services designed to meet every aspect of your property journey
                </p>
              </div>

              <div className="space-y-16">
                {mainServices.map((service, index) => {
                  const IconComponent = service.icon
                  // Create anchor ID based on service title
                  const anchorId = service.title.toLowerCase().includes('residential') ? 'residential' :
                                  service.title.toLowerCase().includes('commercial') ? 'commercial' :
                                  service.title.toLowerCase().includes('project') ? 'project-marketing' :
                                  'service-' + index
                  return (
                    <div key={service.title} id={anchorId} className={`grid lg:grid-cols-2 gap-12 items-center ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                      {/* Content */}
                      <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                        <div className={`w-16 h-16 bg-gradient-to-r ${service.color} rounded-2xl flex items-center justify-center mb-6`}>
                          <IconComponent className="text-white" size={32} />
                        </div>
                        
                        <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
                          {service.title}
                        </h3>
                        
                        <p className="text-lg text-gray-600 leading-relaxed mb-6">
                          {service.description}
                        </p>

                        <div className="grid md:grid-cols-2 gap-6">
                          <div>
                            <h4 className="font-bold text-gray-900 mb-3 flex items-center">
                              <CheckCircle className="text-burgundy-600 mr-2" size={18} />
                              What We Offer
                            </h4>
                            <ul className="space-y-2">
                              {service.features.slice(0, 3).map((feature, idx) => (
                                <li key={idx} className="text-gray-600 text-sm flex items-start">
                                  <div className="w-1.5 h-1.5 bg-burgundy-600 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                                  {feature}
                                </li>
                              ))}
                            </ul>
                          </div>
                          
                          <div>
                            <h4 className="font-bold text-gray-900 mb-3 flex items-center">
                              <Star className="text-burgundy-600 mr-2" size={18} />
                              Key Benefits
                            </h4>
                            <ul className="space-y-2">
                              {service.benefits.slice(0, 3).map((benefit, idx) => (
                                <li key={idx} className="text-gray-600 text-sm flex items-start">
                                  <div className="w-1.5 h-1.5 bg-burgundy-600 rounded-full mr-3 mt-2 flex-shrink-0"></div>
                                  {benefit}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>

                      {/* Visual */}
                      <div className={index % 2 === 1 ? 'lg:col-start-1' : ''}>
                        <Card className="border-0 bg-white shadow-xl">
                          <CardContent className="p-8">
                            <div className="text-center">
                              <div className={`w-20 h-20 bg-gradient-to-r ${service.color} rounded-3xl flex items-center justify-center mb-6 mx-auto`}>
                                <IconComponent className="text-white" size={40} />
                              </div>
                              <h4 className="text-xl font-bold text-gray-900 mb-4">Ready to Get Started?</h4>
                              <p className="text-gray-600 mb-6">
                                Connect with our experts for personalized consultation and solutions.
                              </p>
                              <Button 
                                className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 hover:from-burgundy-700 hover:to-burgundy-800 text-white px-6 py-3 rounded-full font-semibold"
                                asChild
                              >
                                <Link href="/contact">
                                  Get Consultation
                                  <ArrowRight className="ml-2" size={16} />
                                </Link>
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Specialized Services Section */}
      <section id="specialized-services" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <div className="text-center mb-16">
                <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
                  <Award size={16} className="mr-2" />
                  Specialized Services
                </Badge>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                  Additional <span className="text-burgundy-600">Expert</span> Services
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Comprehensive support services to ensure a complete and seamless real estate experience
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {/* Interior & Vastu Consultation */}
                <Card id="interior-vastu" className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-orange-600 to-orange-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Palette className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">Interior & Vastu Consultation</h4>
                    <p className="text-gray-600 leading-relaxed mb-6">
                      Connecting clients with reliable interior designers and certified Vastu experts for harmonious living spaces.
                    </p>
                    <div className="space-y-3 text-left">
                      <div className="flex items-start">
                        <CheckCircle className="text-orange-600 mr-2 mt-1 flex-shrink-0" size={14} />
                        <span className="text-sm text-gray-600">Professional interior design consultation</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle className="text-orange-600 mr-2 mt-1 flex-shrink-0" size={14} />
                        <span className="text-sm text-gray-600">Certified Vastu experts for optimal energy flow</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle className="text-orange-600 mr-2 mt-1 flex-shrink-0" size={14} />
                        <span className="text-sm text-gray-600">Complete home makeover solutions</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle className="text-orange-600 mr-2 mt-1 flex-shrink-0" size={14} />
                        <span className="text-sm text-gray-600">Budget-friendly design options</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Bulk Deal & Group Booking */}
                <Card id="bulk-deals" className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-indigo-600 to-indigo-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Users className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">Bulk Deal & Group Booking</h4>
                    <p className="text-gray-600 leading-relaxed mb-6">
                      Special pricing and customized solutions for investors, corporates, or families buying in groups.
                    </p>
                    <div className="space-y-3 text-left">
                      <div className="flex items-start">
                        <CheckCircle className="text-indigo-600 mr-2 mt-1 flex-shrink-0" size={14} />
                        <span className="text-sm text-gray-600">Exclusive bulk pricing and discounts</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle className="text-indigo-600 mr-2 mt-1 flex-shrink-0" size={14} />
                        <span className="text-sm text-gray-600">Corporate housing solutions</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle className="text-indigo-600 mr-2 mt-1 flex-shrink-0" size={14} />
                        <span className="text-sm text-gray-600">Family group booking assistance</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle className="text-indigo-600 mr-2 mt-1 flex-shrink-0" size={14} />
                        <span className="text-sm text-gray-600">Investment portfolio development</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Additional Support Services */}
                <Card className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group md:col-span-2 lg:col-span-1">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-teal-600 to-teal-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Handshake className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">Additional Support Services</h4>
                    <p className="text-gray-600 leading-relaxed mb-6">
                      Comprehensive support services to ensure smooth transactions and complete satisfaction.
                    </p>
                    <div className="space-y-3 text-left">
                      <div className="flex items-start">
                        <CheckCircle className="text-teal-600 mr-2 mt-1 flex-shrink-0" size={14} />
                        <span className="text-sm text-gray-600">Home loan assistance and guidance</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle className="text-teal-600 mr-2 mt-1 flex-shrink-0" size={14} />
                        <span className="text-sm text-gray-600">Legal documentation support</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle className="text-teal-600 mr-2 mt-1 flex-shrink-0" size={14} />
                        <span className="text-sm text-gray-600">Property registration assistance</span>
                      </div>
                      <div className="flex items-start">
                        <CheckCircle className="text-teal-600 mr-2 mt-1 flex-shrink-0" size={14} />
                        <span className="text-sm text-gray-600">After-sales support and maintenance</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Our Services Section */}
      <section className="py-20 bg-textured-light">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <div className="text-center mb-16">
                <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
                  <Shield size={16} className="mr-2" />
                  Why Choose Us
                </Badge>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                  Why Our <span className="text-burgundy-600">Services</span> Stand Out
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Experience the difference with our comprehensive approach and commitment to excellence
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-full flex items-center justify-center mb-6 mx-auto">
                    <Target className="text-white" size={28} />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-3">Personalized Approach</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Tailored solutions designed specifically for your unique requirements and budget.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center mb-6 mx-auto">
                    <TrendingUp className="text-white" size={28} />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-3">Market Expertise</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Deep understanding of Ahmedabad's real estate market with data-driven insights.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-600 to-green-700 rounded-full flex items-center justify-center mb-6 mx-auto">
                    <Shield className="text-white" size={28} />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-3">Trusted Process</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    Transparent dealings with complete documentation and legal compliance.
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-600 to-purple-700 rounded-full flex items-center justify-center mb-6 mx-auto">
                    <Award className="text-white" size={28} />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-3">Proven Results</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">
                    1000+ satisfied clients and successful transactions across Ahmedabad.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <div className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-3xl p-8 lg:p-12 text-center text-white">
                <h3 className="text-3xl lg:text-4xl font-bold mb-4">Ready to Experience Our Services?</h3>
                <p className="text-xl text-burgundy-100 mb-8 max-w-2xl mx-auto">
                  Let's discuss your real estate needs and find the perfect solution for you. Our expert team is ready to assist you every step of the way.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                  <Button
                    size="lg"
                    className="bg-white text-burgundy-600 hover:bg-gray-100 px-8 py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                    asChild
                  >
                    <Link href="/contact">
                      Get Free Consultation
                      <ArrowRight className="ml-2" size={20} />
                    </Link>
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    className="border-2 border-white/30 text-white hover:bg-white/10 hover:border-white/50 px-8 py-4 rounded-full font-semibold transition-all duration-300 bg-transparent"
                    asChild
                  >
                    <Link href="/projects">
                      Browse Properties
                      <Building2 className="ml-2" size={20} />
                    </Link>
                  </Button>
                </div>

                <div className="grid md:grid-cols-3 gap-6 text-center">
                  <div className="flex items-center justify-center space-x-2">
                    <Phone className="text-yellow-300" size={20} />
                    <span className="text-sm">+91 81411 12929</span>
                  </div>
                  <div className="flex items-center justify-center space-x-2">
                    <Mail className="text-yellow-300" size={20} />
                    <span className="text-sm"><EMAIL></span>
                  </div>
                  <div className="flex items-center justify-center space-x-2">
                    <MapPin className="text-yellow-300" size={20} />
                    <span className="text-sm">Prahlad Nagar, Ahmedabad</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
