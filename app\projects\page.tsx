import { Metadata } from "next"
import ProjectsPageContent from "./projects-content"

export const metadata: Metadata = {
  title: "Projects - Dwelling Desire | New Projects & Resale Properties in Ahmedabad",
  description: "Explore premium new projects and resale properties in Ahmedabad. Find luxury residential and commercial properties with expert guidance from Dwelling Desire.",
  keywords: "new projects ahmedabad, resale properties, luxury properties, residential projects, commercial properties, dwelling desire projects",
  openGraph: {
    title: "Projects - Dwelling Desire | Premium Properties in Ahmedabad",
    description: "Discover new projects and resale properties with expert guidance",
    images: ["/images/projects/projects-og.jpg"],
  },
}

export default function ProjectsPage() {
  return <ProjectsPageContent />
}
