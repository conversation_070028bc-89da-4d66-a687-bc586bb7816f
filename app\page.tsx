import type { Metadata } from "next"
import AnimatedHero from "@/components/sections/animated-hero"
import Services from "@/components/sections/services"
import FeaturedProjects from "@/components/sections/featured-projects"
import About from "@/components/sections/about"
import Testimonials from "@/components/sections/testimonials"
import Insights from "@/components/sections/insights"
import Contact from "@/components/sections/contact"
import DeveloperLogos from "@/components/sections/developer-logos"
import ClientLogos from "@/components/sections/client-logos"

export const metadata: Metadata = {
  title: "Dwelling Desire - Luxury Real Estate in Ahmedabad | Buy, Sell, Lease, Invest",
  description:
    "Premium real estate services in Ahmedabad. Find luxury properties, expert consultation for buying, selling, leasing, and investment opportunities.",
  keywords: "real estate Ahmedabad, luxury properties, buy sell lease invest, Prahlad Nagar, Gujarat real estate",
  openGraph: {
    title: "Dwelling Desire - Your Dream Property Awaits",
    description: "Premium real estate services in Ahmedabad",
    images: ["/images/logo.png"],
  },
}

export default function HomePage() {
  return (
    <main className="min-h-screen">
      <AnimatedHero />
      <About />
      <Services />
      <FeaturedProjects />
      <Testimonials />
      <Insights />
      <DeveloperLogos />
      <ClientLogos />
      <Contact />
    </main>
  )
}
