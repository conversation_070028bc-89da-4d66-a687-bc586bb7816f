"use client"

import { useState, useEffect } from "react"

interface TypingEffectProps {
  text: string
  speed?: number // milliseconds per character
  delay?: number // initial delay before typing starts
}

export default function TypingEffect({ text, speed = 70, delay = 500 }: TypingEffectProps) {
  const [displayedText, setDisplayedText] = useState("")
  const [isTyping, setIsTyping] = useState(false)

  useEffect(() => {
    let timeoutId: NodeJS.Timeout
    let charIndex = 0

    const startTyping = () => {
      setIsTyping(true)
      setDisplayedText("") // Reset text for re-render if component remounts
      charIndex = 0

      const typeChar = () => {
        if (charIndex < text.length) {
          setDisplayedText((prev) => prev + text.charAt(charIndex))
          charIndex++
          timeoutId = setTimeout(typeChar, speed)
        } else {
          setIsTyping(false)
        }
      }

      timeoutId = setTimeout(typeChar, delay) // Initial delay before typing starts
    }

    startTyping()

    return () => {
      clearTimeout(timeoutId)
    }
  }, [text, speed, delay])

  return (
    <>
      {displayedText}
      <span
        className={`inline-block w-0.5 h-full bg-burgundy-600 align-bottom ml-1 animate-pulse ${isTyping ? "" : "opacity-0"}`}
      ></span>
    </>
  )
}
