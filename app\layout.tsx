import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter, Playfair_Display } from "next/font/google"
import "./globals.css"
import Navbar from "@/components/layout/navbar"
import Footer from "@/components/layout/footer"
import StickyContact from "@/components/layout/sticky-contact"
import PreloaderProvider from "@/components/providers/preloader-provider"
import PageTransition from "@/components/ui/page-transition"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
})

const playfair = Playfair_Display({
  subsets: ["latin"],
  variable: "--font-playfair",
})

export const metadata: Metadata = {
  title: "Dwelling Desire - Luxury Real Estate in Ahmedabad",
  description:
    "Premium real estate services in Ahmedabad. Find luxury properties, expert consultation for buying, selling, leasing, and investment opportunities.",
  keywords: "real estate Ahmedabad, luxury properties, buy sell lease invest, Prahlad Nagar, Gujarat real estate",
  authors: [{ name: "Dwelling Desire" }],
  creator: "Dwelling Desire",
  publisher: "Dwelling Desire",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://dwellingdesire.com"),
  alternates: {
    canonical: "/",
  },
  icons: {
    icon: [
      { url: "/favicon/favicon-16x16.png", sizes: "16x16", type: "image/png" },
      { url: "/favicon/favicon-32x32.png", sizes: "32x32", type: "image/png" },
      { url: "/favicon/favicon.ico", sizes: "any" }
    ],
    apple: [
      { url: "/favicon/apple-touch-icon.png", sizes: "180x180", type: "image/png" }
    ],
    other: [
      { rel: "android-chrome-192x192", url: "/favicon/android-chrome-192x192.png" },
      { rel: "android-chrome-512x512", url: "/favicon/android-chrome-512x512.png" }
    ]
  },
  manifest: "/favicon/site.webmanifest",
  openGraph: {
    title: "Dwelling Desire - Your Dream Property Awaits",
    description: "Premium real estate services in Ahmedabad",
    url: "https://dwellingdesire.com",
    siteName: "Dwelling Desire",
    images: [
      {
        url: "/images/logo.png",
        width: 1200,
        height: 630,
        alt: "Dwelling Desire - Luxury Real Estate",
      },
    ],
    locale: "en_US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Dwelling Desire - Luxury Real Estate in Ahmedabad",
    description: "Premium real estate services in Ahmedabad",
    images: ["/images/logo.png"],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className={`${inter.variable} ${playfair.variable}`} suppressHydrationWarning>
      <body className={`${inter.className} antialiased`}>
        <PreloaderProvider>
          <Navbar />
          <PageTransition variant="slideUp" showLoadingBar={true}>
            {children}
          </PageTransition>
          <Footer />
          <StickyContact />
        </PreloaderProvider>
      </body>
    </html>
  )
}
