@tailwind base;
@tailwind components;
@tailwind utilities;

/* Force scrollbar to always be present to prevent layout shifts */
html {
  overflow-y: scroll !important;
}

/* Hide the scrollbar visually but keep the space reserved */
::-webkit-scrollbar {
  width: 17px; /* Standard scrollbar width */
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* Firefox */
html {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

/* Ensure body doesn't shift */
body {
  margin: 0;
  padding: 0;
  width: 100%;
  overflow-x: hidden;
}

/* Mobile devices - hide scrollbar completely */
@media (max-width: 768px) {
  html {
    overflow-y: auto !important;
  }

  ::-webkit-scrollbar {
    width: 0px;
    background: transparent;
  }

  html {
    scrollbar-width: none;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    overflow-x: hidden;
  }
  html {
    overflow-x: hidden;
  }
}

/* Minimalist Animations */
@keyframes smooth-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fade-in-up-small {
  0% {
    opacity: 0;
    transform: translateY(16px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in-minimal {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes underline-expand {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 100%;
    opacity: 1;
  }
}

@keyframes float-minimal {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes float-minimal-reverse {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(8px);
  }
}

@keyframes pulse-subtle {
  0%,
  100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes pulse-gentle {
  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

@keyframes line-extend {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 6rem;
    opacity: 1;
  }
}

@keyframes line-extend-reverse {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 8rem;
    opacity: 1;
  }
}

@keyframes dot-float {
  0%,
  100% {
    transform: translateY(0px);
    opacity: 0.3;
  }
  50% {
    transform: translateY(-5px);
    opacity: 0.6;
  }
}

@keyframes icon-float-minimal {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-8px) rotate(5deg);
  }
}

@keyframes bounce-minimal {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-6px);
  }
}

@keyframes scroll-minimal {
  0%,
  100% {
    transform: translateY(0);
    opacity: 1;
  }
  50% {
    transform: translateY(6px);
    opacity: 0.5;
  }
}

@keyframes scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
}

/* Animation Classes */
.animate-smooth-float {
  animation: smooth-float 4s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
  /* Override initial Tailwind classes and ensure final state is maintained */
  opacity: 1 !important;
  transform: translateY(0) !important;
}

.animate-fade-in-up-small {
  animation: fade-in-up-small 0.6s ease-out forwards;
  /* Override initial Tailwind classes and ensure final state is maintained */
  opacity: 1 !important;
  transform: translateY(0) !important;
}

.animate-slide-in-minimal {
  animation: slide-in-minimal 0.8s ease-out forwards;
}

.animate-underline-expand {
  animation: underline-expand 1.5s ease-out forwards;
  animation-delay: 0.8s;
}

.animate-float-minimal {
  animation: float-minimal 6s ease-in-out infinite;
}

.animate-float-minimal-reverse {
  animation: float-minimal-reverse 8s ease-in-out infinite;
}

.animate-pulse-subtle {
  animation: pulse-subtle 4s ease-in-out infinite;
}

.animate-pulse-gentle {
  animation: pulse-gentle 2s ease-in-out infinite;
}

.animate-line-extend {
  animation: line-extend 2s ease-out forwards;
  animation-delay: 1s;
}

.animate-line-extend-reverse {
  animation: line-extend-reverse 2.5s ease-out forwards;
  animation-delay: 1.5s;
}

.animate-dot-float {
  animation: dot-float 3s ease-in-out infinite;
}

.animate-icon-float-minimal {
  animation: icon-float-minimal 4s ease-in-out infinite;
}

.animate-bounce-minimal {
  animation: bounce-minimal 2s ease-in-out infinite;
}

.animate-scroll-minimal {
  animation: scroll-minimal 2s ease-in-out infinite;
}

.animate-scroll {
  animation: scroll 45s linear infinite;
}

.animate-pulse {
  animation: pulse 1s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.group-hover\:pause:hover {
  animation-play-state: paused;
}

.animate-spin-slow {
  animation: spin 8s linear infinite;
}

.animate-spin-very-slow {
  animation: spin 20s linear infinite;
}

/* Smooth Typewriter Animation */
@keyframes typewriter-smooth {
  0% {
    width: 0;
    opacity: 1;
  }
  95% {
    width: 100%;
    opacity: 1;
  }
  100% {
    width: 100%;
    opacity: 1;
    border-right-color: transparent;
  }
}

@keyframes cursor-blink {
  0%, 50% {
    border-right-color: #b91c5c;
  }
  51%, 100% {
    border-right-color: transparent;
  }
}

.typewriter-smooth {
  overflow: hidden;
  border-right: 2px solid #b91c5c;
  white-space: nowrap;
  display: inline-block;
  animation:
    typewriter-smooth 3s ease-out 1s both,
    cursor-blink 0.8s infinite 1s;
}

.typewriter-smooth::after {
  content: '';
  animation: cursor-blink 0.8s infinite 4s;
}

/* Animation Classes */
.animate-on-scroll {
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.animate-on-scroll.animate-in {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* Ensure smooth transitions without conflicts */
.animate-on-scroll.opacity-100 {
  opacity: 1 !important;
}

/* Fallback for when animations don't load */
@media (prefers-reduced-motion: reduce) {
  .animate-on-scroll {
    opacity: 1 !important;
    transform: none !important;
  }
}

/* Custom Colors */
.text-burgundy-600 {
  color: #b91c5c;
}

.text-burgundy-700 {
  color: #9f1c5c;
}

.bg-burgundy-50 {
  background-color: #fdf2f8;
}

.bg-burgundy-100 {
  background-color: #fce7f3;
}

.bg-burgundy-200 {
  background-color: #fbcfe8;
}

.bg-burgundy-500 {
  background-color: #ec4899;
}

.bg-burgundy-600 {
  background-color: #b91c5c;
}

.bg-burgundy-700 {
  background-color: #9f1c5c;
}

.border-burgundy-200 {
  border-color: #fbcfe8;
}

.border-burgundy-300 {
  border-color: #f9a8d4;
}

.border-burgundy-400 {
  border-color: #f472b6;
}

.border-burgundy-500 {
  border-color: #ec4899;
}

.border-burgundy-600 {
  border-color: #b91c5c;
}

.hover\:bg-burgundy-50:hover {
  background-color: #fdf2f8;
}

.hover\:bg-burgundy-200:hover {
  background-color: #fbcfe8;
}

.hover\:bg-burgundy-600:hover {
  background-color: #b91c5c;
}

.hover\:bg-burgundy-700:hover {
  background-color: #9f1c5c;
}

.hover\:text-burgundy-600:hover {
  color: #b91c5c;
}

.hover\:text-burgundy-700:hover {
  color: #9f1c5c;
}

.hover\:border-burgundy-200:hover {
  border-color: #fbcfe8;
}

.hover\:border-burgundy-400:hover {
  border-color: #f472b6;
}

.hover\:border-burgundy-500:hover {
  border-color: #ec4899;
}

.text-burgundy-300 {
  color: #f9a8d4;
}

/* Dropdown Menu Enhancements */
.dropdown-menu {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #b91c5c;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9f1c5c;
}

/* Typography */
.font-playfair {
  font-family: var(--font-playfair);
}

/* Line Clamp Utilities */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Client Logo Carousel */
.client-logo-featured {
  filter: none !important;
  opacity: 1 !important;
  transform: scale(1.1);
}

/* Navigation Dropdown Fixes */
.nav-dropdown {
  z-index: 60;
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.nav-dropdown::before {
  content: "";
  position: absolute;
  top: -8px;
  left: 20px;
  width: 16px;
  height: 16px;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: none;
  border-right: none;
  transform: rotate(45deg);
  z-index: -1;
}

/* Minimalist Focus States */
.focus\:ring-burgundy-500:focus {
  --tw-ring-color: #ec4899;
}

.focus\:border-burgundy-500:focus {
  border-color: #ec4899;
}

/* Hover Effects */
.hover\:shadow-xl:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Background Colors */
.bg-yellow-100 {
  background-color: #fef3c7;
}

.bg-yellow-200 {
  background-color: #fde68a;
}

.bg-green-100 {
  background-color: #dcfce7;
}

.bg-green-200 {
  background-color: #bbf7d0;
}

.text-yellow-600 {
  color: #d97706;
}

.text-green-600 {
  color: #16a34a;
}

/* Textured Background Pattern - Similar to RERA Certificate */
.bg-textured-light {
  background-color: #f8f9fa;
  background-image:
    radial-gradient(circle at 1px 1px, rgba(0,0,0,0.03) 1px, transparent 0),
    linear-gradient(45deg, rgba(0,0,0,0.01) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(0,0,0,0.01) 25%, transparent 25%);
  background-size: 20px 20px, 40px 40px, 40px 40px;
  background-position: 0 0, 0 0, 20px 20px;
}

.bg-textured-white {
  background-color: #ffffff;
  background-image:
    radial-gradient(circle at 1px 1px, rgba(0,0,0,0.02) 1px, transparent 0),
    linear-gradient(45deg, rgba(0,0,0,0.005) 25%, transparent 25%),
    linear-gradient(-45deg, rgba(0,0,0,0.005) 25%, transparent 25%);
  background-size: 15px 15px, 30px 30px, 30px 30px;
  background-position: 0 0, 0 0, 15px 15px;
}
