import type { Metadata } from "next"
import CalculatorPageContent from "./calculator-content"

export const metadata: Metadata = {
  title: "Real Estate Calculator - EMI, ROI & Rent vs Buy | Dwelling Desire",
  description: "Use our comprehensive real estate calculators for EMI calculation, ROI analysis, and rent vs buy comparison. Make informed property investment decisions in Ahmedabad with Dwelling Desire's free tools.",
  keywords: "real estate calculator, EMI calculator, ROI calculator, rent vs buy calculator, property investment calculator, home loan EMI, real estate ROI, ahmedabad property calculator",
  openGraph: {
    title: "Real Estate Calculator Tools - Dwelling Desire",
    description: "Calculate EMI, ROI, and compare rent vs buy options with our comprehensive real estate calculators. Make smart property decisions.",
    url: "/calculator",
    images: [
      {
        url: "/images/logo.png",
        width: 1200,
        height: 630,
        alt: "Real Estate Calculator - Dwelling Desire",
      },
    ],
  },
}

export default function CalculatorPage() {
  return <CalculatorPageContent />
}
