"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { 
  MapPin, Bed, Bath, Square, Star, Calendar, Camera, Building, 
  ArrowLeft, Phone, MessageCircle, Share2, Heart, Car, Zap,
  Shield, Droplets, Wifi, TreePine, Dumbbell, Users, Home,
  Clock, IndianRupee, FileText, Map, ImageIcon, Settings,
  Navigation, Hospital, GraduationCap, ShoppingBag, Plane
} from "lucide-react"

interface Project {
  id: number
  title: string
  location: string
  price: string
  type: string
  status: string
  image: string
  beds: string
  baths: string
  area: string
  rating: number
  possession: string
  photoCount: number
  features: string[]
  description: string
  builder: string
  rera: string
  totalFloors: number
  totalUnits: number
  launchDate: string
  completionDate: string
  pricePerSqft: string
  maintenanceCharges: string
  parkingCharges: string
  floorPlan: string
  masterPlan: string
  gallery: string[]
  amenities: string[]
  specifications: {
    structure: string
    walls: string
    flooring: string
    kitchen?: string
    bathroom?: string
    ceiling?: string
    doors: string
    windows: string
    electrical: string
    plumbing: string
  }
  connectivity: {
    airport: string
    railwayStation: string
    busStand: string
    metro: string
    highway: string
  }
  nearbyPlaces: Array<{
    name: string
    distance: string
    type: string
  }>
}

interface Props {
  project: Project
}

export default function ProjectDetailContent({ project }: Props) {
  const [selectedImage, setSelectedImage] = useState(0)
  const [isLiked, setIsLiked] = useState(false)

  // WhatsApp inquiry function
  const handleWhatsAppInquiry = () => {
    const phoneNumber = "918141112929"
    const propertyLink = `${window.location.href}`
    const message = `Hi! I'm interested in *${project.title}* located at ${project.location}.

Property Details:
Location: ${project.location}
Price: ${project.price}
Type: ${project.beds}
Area: ${project.area}

Property Link:
${propertyLink}

Could you please share more details about this property? I would like to schedule a site visit.

Thank you!`

    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`
    window.open(whatsappUrl, '_blank')
  }

  // Phone call function
  const handlePhoneCall = () => {
    window.location.href = "tel:+918141112929"
  }

  // Share function
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: project.title,
          text: `Check out ${project.title} in ${project.location}`,
          url: window.location.href,
        })
      } catch (error) {
        console.log('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert('Link copied to clipboard!')
    }
  }

  const getTypeIcon = (type: string) => {
    if (type.toLowerCase().includes('office') || type.toLowerCase().includes('commercial')) {
      return <Building className="text-gray-400" size={20} />
    }
    return <Bed className="text-gray-400" size={20} />
  }

  const getPlaceIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'healthcare': return <Hospital className="text-red-500" size={16} />
      case 'education': return <GraduationCap className="text-blue-500" size={16} />
      case 'shopping': return <ShoppingBag className="text-green-500" size={16} />
      case 'recreation': return <TreePine className="text-green-600" size={16} />
      case 'it hub': return <Building className="text-purple-500" size={16} />
      default: return <MapPin className="text-gray-500" size={16} />
    }
  }

  return (
    <div className="min-h-screen bg-textured-light">
      {/* Header with Back Button */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-6 mt-20">
          <div className="flex items-center justify-between">
            <Button variant="ghost" asChild className="text-burgundy-600 hover:bg-burgundy-50">
              <Link href="/projects">
                <ArrowLeft size={20} className="mr-2" />
                Back to Projects
              </Link>
            </Button>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsLiked(!isLiked)}
                className={`${isLiked ? 'text-red-500' : 'text-gray-500'} hover:bg-gray-100`}
              >
                <Heart size={20} className={isLiked ? 'fill-current' : ''} />
              </Button>
              <Button variant="ghost" size="sm" onClick={handleShare} className="text-gray-500 hover:bg-gray-100">
                <Share2 size={20} />
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Image Gallery */}
            <Card className="overflow-hidden">
              <div className="relative">
                <Image
                  src={(project.gallery && project.gallery[selectedImage]) || project.image}
                  alt={project.title}
                  width={800}
                  height={500}
                  className="w-full h-64 md:h-96 object-cover"
                />
                <div className="absolute top-4 left-4 flex gap-2">
                  <Badge
                    className={`${
                      project.type === "New Launch"
                        ? "bg-green-500"
                        : project.type === "Ready to Move"
                          ? "bg-blue-500"
                          : "bg-purple-500"
                    } text-white`}
                  >
                    {project.type}
                  </Badge>
                  <Badge variant="secondary" className="bg-white/90 text-gray-700">
                    {project.status}
                  </Badge>
                </div>
                <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center space-x-1">
                  <Star className="text-yellow-500 fill-current" size={16} />
                  <span className="text-sm font-medium">{project.rating}</span>
                </div>
                <div className="absolute bottom-4 right-4 bg-black/70 backdrop-blur-sm rounded-full px-3 py-1 flex items-center space-x-1">
                  <Camera className="text-white" size={16} />
                  <span className="text-sm font-medium text-white">{project.photoCount}</span>
                </div>
              </div>
              
              {/* Thumbnail Gallery */}
              <div className="p-4">
                <div className="flex space-x-2 overflow-x-auto">
                  {project.gallery && project.gallery.length > 0 ? (
                    project.gallery.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedImage(index)}
                        className={`flex-shrink-0 w-20 h-16 rounded-lg overflow-hidden border-2 ${
                          selectedImage === index ? 'border-burgundy-500' : 'border-gray-200'
                        }`}
                      >
                        <Image
                          src={image}
                          alt={`${project.title} ${index + 1}`}
                          width={80}
                          height={64}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))
                  ) : (
                    <div className="w-full text-center py-4">
                      <p className="text-gray-500 text-sm">Gallery images will be available soon.</p>
                    </div>
                  )}
                </div>
              </div>
            </Card>

            {/* Project Details */}
            <Card>
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div>
                    <CardTitle className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                      {project.title}
                    </CardTitle>
                    <div className="flex items-center text-gray-600 mb-4">
                      <MapPin size={18} className="mr-2" />
                      <span className="text-lg">{project.location}</span>
                    </div>
                    <div className="text-3xl font-bold text-burgundy-600 mb-4">{project.price}</div>
                  </div>
                </div>
                <p className="text-gray-600 leading-relaxed">{project.description}</p>
              </CardHeader>
            </Card>

            {/* Detailed Information Tabs */}
            <Card>
              <CardContent className="p-0">
                <Tabs defaultValue="overview" className="w-full">
                  <TabsList className="grid w-full grid-cols-5 rounded-none border-b">
                    <TabsTrigger value="overview" className="flex items-center space-x-2">
                      <Home size={16} />
                      <span className="hidden sm:inline">Overview</span>
                    </TabsTrigger>
                    <TabsTrigger value="amenities" className="flex items-center space-x-2">
                      <Settings size={16} />
                      <span className="hidden sm:inline">Amenities</span>
                    </TabsTrigger>
                    <TabsTrigger value="specifications" className="flex items-center space-x-2">
                      <FileText size={16} />
                      <span className="hidden sm:inline">Specs</span>
                    </TabsTrigger>
                    <TabsTrigger value="location" className="flex items-center space-x-2">
                      <Navigation size={16} />
                      <span className="hidden sm:inline">Location</span>
                    </TabsTrigger>
                    <TabsTrigger value="floorplan" className="flex items-center space-x-2">
                      <Map size={16} />
                      <span className="hidden sm:inline">Plans</span>
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="p-6 space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <h3 className="text-xl font-semibold text-gray-900">Project Information</h3>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Builder</span>
                            <span className="font-medium">{project.builder}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Total Floors</span>
                            <span className="font-medium">{project.totalFloors}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Total Units</span>
                            <span className="font-medium">{project.totalUnits}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Launch Date</span>
                            <span className="font-medium">{project.launchDate}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Possession</span>
                            <span className="font-medium">{project.possession}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        <h3 className="text-xl font-semibold text-gray-900">Pricing Details</h3>
                        <div className="space-y-3">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Price per Sq Ft</span>
                            <span className="font-medium">{project.pricePerSqft}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Maintenance</span>
                            <span className="font-medium">{project.maintenanceCharges}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Parking Charges</span>
                            <span className="font-medium">{project.parkingCharges}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="bg-gray-50 p-4 rounded-lg">
                      <h4 className="font-semibold text-gray-900 mb-2">RERA Registration</h4>
                      <p className="text-sm text-gray-600 font-mono break-all sm:break-normal overflow-wrap-anywhere">{project.rera}</p>
                    </div>
                  </TabsContent>

                  <TabsContent value="amenities" className="p-6">
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                      {project.amenities && project.amenities.length > 0 ? (
                        project.amenities.map((amenity, index) => (
                          <div key={index} className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                            <div className="w-2 h-2 bg-burgundy-500 rounded-full"></div>
                            <span className="text-sm font-medium text-gray-700">{amenity}</span>
                          </div>
                        ))
                      ) : (
                        <div className="col-span-full text-center py-8">
                          <p className="text-gray-500 text-lg">Amenities information will be available soon.</p>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="specifications" className="p-6">
                    <div className="space-y-4">
                      {project.specifications && Object.keys(project.specifications).length > 0 ? (
                        Object.entries(project.specifications).map(([key, value]) => (
                          <div key={key} className="flex flex-col sm:flex-row sm:justify-between py-3 border-b border-gray-100 last:border-b-0">
                            <span className="text-gray-600 capitalize font-medium mb-1 sm:mb-0">
                              {key.replace(/([A-Z])/g, ' $1').trim()}
                            </span>
                            <span className="font-medium text-gray-900 sm:text-right sm:max-w-md">{value}</span>
                          </div>
                        ))
                      ) : (
                        <div className="text-center py-8">
                          <p className="text-gray-500 text-lg">Specifications information will be available soon.</p>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="location" className="p-6 space-y-6">
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Connectivity</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {project.connectivity && Object.keys(project.connectivity).length > 0 ? (
                          Object.entries(project.connectivity).map(([key, value]) => (
                            <div key={key} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                              <span className="text-gray-600 capitalize">
                                {key.replace(/([A-Z])/g, ' $1').trim()}
                              </span>
                              <span className="font-medium text-burgundy-600">{value}</span>
                            </div>
                          ))
                        ) : (
                          <div className="col-span-2 text-center py-8">
                            <p className="text-gray-500">Connectivity information will be available soon.</p>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-4">Nearby Places</h3>
                      <div className="space-y-3">
                        {project.nearbyPlaces.map((place, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-3">
                              {getPlaceIcon(place.type)}
                              <div>
                                <span className="font-medium text-gray-900">{place.name}</span>
                                <span className="text-sm text-gray-500 ml-2">({place.type})</span>
                              </div>
                            </div>
                            <span className="text-sm font-medium text-burgundy-600">{place.distance}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </TabsContent>

                  <TabsContent value="floorplan" className="p-6">
                    <div className="space-y-6">
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Floor Plan</h3>
                        <div className="bg-gray-100 rounded-lg p-8 text-center">
                          <ImageIcon size={48} className="mx-auto text-gray-400 mb-4" />
                          <p className="text-gray-600">Floor plan will be available soon</p>
                          <p className="text-sm text-gray-500 mt-2">Contact us for detailed floor plans</p>
                        </div>
                      </div>
                      
                      <div>
                        <h3 className="text-xl font-semibold text-gray-900 mb-4">Master Plan</h3>
                        <div className="bg-gray-100 rounded-lg p-8 text-center">
                          <Map size={48} className="mx-auto text-gray-400 mb-4" />
                          <p className="text-gray-600">Master plan will be available soon</p>
                          <p className="text-sm text-gray-500 mt-2">Contact us for detailed master plan</p>
                        </div>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Info Card */}
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle className="text-lg">Property Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                    {getTypeIcon(project.beds)}
                    <div>
                      <p className="text-xs text-gray-500">Type</p>
                      <p className="font-medium text-sm">{project.beds}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 p-3 bg-gray-50 rounded-lg">
                    <Square className="text-gray-400" size={20} />
                    <div>
                      <p className="text-xs text-gray-500">Area</p>
                      <p className="font-medium text-sm">{project.area}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 p-3 bg-burgundy-50 rounded-lg">
                    <Calendar className="text-burgundy-600" size={20} />
                    <div>
                      <p className="text-xs text-burgundy-700">Possession</p>
                      <p className="font-medium text-sm text-burgundy-600">{project.possession}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 p-3 bg-green-50 rounded-lg">
                    <Star className="text-yellow-500 fill-current" size={20} />
                    <div>
                      <p className="text-xs text-gray-500">Rating</p>
                      <p className="font-medium text-sm">{project.rating}/5</p>
                    </div>
                  </div>
                </div>

                {/* Contact Buttons */}
                <div className="space-y-3 pt-4 border-t">
                  <Button 
                    onClick={handleWhatsAppInquiry}
                    className="w-full bg-green-600 hover:bg-green-700 text-white"
                  >
                    <MessageCircle size={18} className="mr-2" />
                    WhatsApp Inquiry
                  </Button>
                  
                  <Button 
                    onClick={handlePhoneCall}
                    variant="outline" 
                    className="w-full border-burgundy-200 text-burgundy-600 hover:bg-burgundy-50"
                  >
                    <Phone size={18} className="mr-2" />
                    Call Now
                  </Button>
                </div>

                <div className="text-center pt-2">
                  <p className="text-sm text-gray-500">
                    Get instant response on WhatsApp
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    Available 24/7 for inquiries
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
