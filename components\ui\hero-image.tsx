/**
 * Hero Image Component with Optimized Loading
 *
 * Image Specifications:
 * - Desktop: 1920x1080px (16:9 aspect ratio)
 * - Tablet: 1024x576px
 * - Mobile: 768x432px
 * - Format: WebP with JPEG fallback
 * - Quality: 85% for optimal balance
 * - Loading: Eager with high priority
 */

import Image from "next/image"

interface HeroImageProps {
  alt?: string
  className?: string
}

export default function HeroImage({
  alt = "Luxury real estate properties in Ahmedabad - Dwelling Desire",
  className = "absolute inset-0 w-full h-full object-cover",
}: HeroImageProps) {
  return (
    <picture>
      <source
        media="(max-width: 768px)"
        srcSet="/placeholder.svg?height=432&width=768&text=Mobile+Luxury+Villa"
        type="image/webp"
      />
      <source
        media="(max-width: 1024px)"
        srcSet="/placeholder.svg?height=576&width=1024&text=Tablet+Luxury+Villa"
        type="image/webp"
      />
      <source
        media="(min-width: 1025px)"
        srcSet="/placeholder.svg?height=1080&width=1920&text=Desktop+Luxury+Villa"
        type="image/webp"
      />
      <Image
        src="/placeholder.svg?height=1080&width=1920&text=Desktop+Luxury+Villa"
        alt={alt}
        width={1920}
        height={1080}
        className={className}
        loading="eager"
        priority
        quality={85}
        sizes="(max-width: 768px) 768px, (max-width: 1024px) 1024px, 1920px"
      />
    </picture>
  )
}
