"use client"

import { useState, useEffect } from "react"

interface UseLottieAnimationReturn {
  animationData: any | null
  isLoading: boolean
  error: string | null
}

export const useLottieAnimation = (animationPath: string): UseLottieAnimationReturn => {
  const [animationData, setAnimationData] = useState<any | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadAnimation = async () => {
      try {
        setIsLoading(true)
        setError(null)

        // Use fetch with cache for better performance
        const response = await fetch(animationPath, {
          cache: 'force-cache',
          headers: {
            'Accept': 'application/json',
          }
        })

        if (!response.ok) {
          throw new Error(`Failed to load animation: ${response.statusText}`)
        }

        const data = await response.json()
        setAnimationData(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load animation")
        console.error("Error loading Lottie animation:", err)
      } finally {
        setIsLoading(false)
      }
    }

    if (animationPath) {
      loadAnimation()
    }
  }, [animationPath])

  return { animationData, isLoading, error }
}

// Predefined animation paths for easy use
export const ANIMATION_PATHS = {
  loading: "/animations/pre-loader.json",
  // Add more animation paths here as needed
  // success: "/animations/success-animation.json",
  // error: "/animations/error-animation.json",
} as const

export default useLottieAnimation
