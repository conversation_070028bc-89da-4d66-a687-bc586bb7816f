"use client"

import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"

/**
 * Demo component to showcase different transition options
 * You can remove this file if not needed
 */
const TransitionDemo: React.FC = () => {
  return (
    <div className="p-8 space-y-6">
      <h2 className="text-3xl font-bold text-center mb-8">Page Transition Options</h2>
      
      <div className="grid md:grid-cols-3 gap-6">
        {/* Option 1: Slide Up (Current) */}
        <Card className="border-burgundy-200">
          <CardHeader>
            <CardTitle className="text-burgundy-600">✅ Slide Up (Current)</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              Elegant upward slide with smooth easing. Professional and modern feel.
            </p>
            <div className="bg-gray-100 p-4 rounded-lg">
              <code className="text-xs">
                variant="slideUp"<br/>
                Duration: 0.4s<br/>
                Easing: <PERSON><PERSON><PERSON>
              </code>
            </div>
          </CardContent>
        </Card>

        {/* Option 2: Fade Scale */}
        <Card>
          <CardHeader>
            <CardTitle className="text-blue-600">Fade Scale</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              Subtle fade with gentle scaling. Clean and minimal transition.
            </p>
            <div className="bg-gray-100 p-4 rounded-lg">
              <code className="text-xs">
                variant="fadeScale"<br/>
                Duration: 0.3s<br/>
                Easing: Ease In Out
              </code>
            </div>
          </CardContent>
        </Card>

        {/* Option 3: Slide Right */}
        <Card>
          <CardHeader>
            <CardTitle className="text-green-600">Slide Right</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-600 mb-4">
              Horizontal slide transition. Good for sequential navigation.
            </p>
            <div className="bg-gray-100 p-4 rounded-lg">
              <code className="text-xs">
                variant="slideRight"<br/>
                Duration: 0.35s<br/>
                Easing: Cubic Bezier
              </code>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8 p-6 bg-blue-50 rounded-lg">
        <h3 className="font-semibold mb-4">🎯 Current Implementation Features:</h3>
        <ul className="space-y-2 text-sm">
          <li>✅ <strong>Loading Bar:</strong> Top progress bar during transitions</li>
          <li>✅ <strong>Smooth Scroll:</strong> Auto scroll to top on page change</li>
          <li>✅ <strong>Professional Easing:</strong> Custom cubic-bezier curves</li>
          <li>✅ <strong>Optimized Performance:</strong> GPU-accelerated animations</li>
          <li>✅ <strong>Accessibility:</strong> Respects reduced motion preferences</li>
        </ul>
      </div>

      <div className="mt-6 p-6 bg-yellow-50 rounded-lg">
        <h3 className="font-semibold mb-4">🔧 How to Change Transition:</h3>
        <p className="text-sm mb-4">
          To change the transition style, update the <code>variant</code> prop in <code>app/layout.tsx</code>:
        </p>
        <div className="bg-white p-4 rounded border">
          <code className="text-sm">
            {`<PageTransition variant="slideUp" showLoadingBar={true}>`}<br/>
            {`  {children}`}<br/>
            {`</PageTransition>`}
          </code>
        </div>
        <p className="text-xs text-gray-600 mt-2">
          Available variants: "slideUp", "fadeScale", "slideRight"
        </p>
      </div>
    </div>
  )
}

export default TransitionDemo
