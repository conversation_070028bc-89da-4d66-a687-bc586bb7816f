"use client"

import React from "react"
import { usePreloader } from "@/components/providers/preloader-provider"
import { useLottieAnimation, ANIMATION_PATHS } from "@/hooks/use-lottie-animation"
import { Button } from "@/components/ui/button"

/**
 * Example component showing how to use the preloader system
 * This is just for demonstration - you can remove this file if not needed
 */
const PreloaderExample: React.FC = () => {
  const { showPreloader, hidePreloader, isLoading } = usePreloader()
  
  // Example of loading a custom animation
  const { animationData: customAnimation, isLoading: animationLoading } = useLottieAnimation(
    ANIMATION_PATHS.loading
  )

  const handleShowPreloader = () => {
    showPreloader(4000) // Show for 4 seconds
  }

  const handleHidePreloader = () => {
    hidePreloader()
  }

  return (
    <div className="p-8 space-y-4">
      <h2 className="text-2xl font-bold">Preloader Example</h2>
      <p className="text-gray-600">
        This component demonstrates how to control the preloader programmatically.
      </p>
      
      <div className="space-x-4">
        <Button 
          onClick={handleShowPreloader}
          disabled={isLoading}
        >
          Show Preloader (4s)
        </Button>
        
        <Button 
          onClick={handleHidePreloader}
          variant="outline"
          disabled={!isLoading}
        >
          Hide Preloader
        </Button>
      </div>

      <div className="mt-4 p-4 bg-gray-100 rounded-lg">
        <h3 className="font-semibold mb-2">Status:</h3>
        <p>Preloader Active: {isLoading ? "Yes" : "No"}</p>
        <p>Animation Loading: {animationLoading ? "Yes" : "No"}</p>
        <p>Custom Animation Loaded: {customAnimation ? "Yes" : "No"}</p>
      </div>

      <div className="mt-4 p-4 bg-blue-50 rounded-lg">
        <h3 className="font-semibold mb-2">Usage Examples:</h3>
        <div className="space-y-2 text-sm">
          <p><strong>1. Show preloader on page navigation:</strong></p>
          <code className="block bg-white p-2 rounded">
            const {`{ showPreloader }`} = usePreloader();<br/>
            showPreloader(3000); // Show for 3 seconds
          </code>
          
          <p><strong>2. Hide preloader manually:</strong></p>
          <code className="block bg-white p-2 rounded">
            const {`{ hidePreloader }`} = usePreloader();<br/>
            hidePreloader();
          </code>
          
          <p><strong>3. Check if preloader is active:</strong></p>
          <code className="block bg-white p-2 rounded">
            const {`{ isLoading }`} = usePreloader();<br/>
            if (isLoading) {`{ /* Do something */ }`}
          </code>
        </div>
      </div>
    </div>
  )
}

export default PreloaderExample
