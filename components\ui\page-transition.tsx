"use client"

import { useEffect, useState } from "react"
import { usePathname } from "next/navigation"
import { motion, AnimatePresence } from "framer-motion"

// Professional transition variants
const transitionVariants = {
  // Option 1: Elegant Slide Up (Recommended)
  slideUp: {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }
  },

  // Option 2: Smooth Fade with Scale
  fadeScale: {
    initial: { opacity: 0, scale: 0.98 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 1.02 },
    transition: { duration: 0.3, ease: "easeInOut" }
  },

  // Option 3: Professional Slide Right
  slideRight: {
    initial: { opacity: 0, x: -30 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: 30 },
    transition: { duration: 0.35, ease: [0.25, 0.46, 0.45, 0.94] }
  }
}

// Loading bar component
const LoadingBar = () => (
  <motion.div
    initial={{ scaleX: 0 }}
    animate={{ scaleX: 1 }}
    exit={{ scaleX: 0 }}
    transition={{ duration: 0.3, ease: "easeInOut" }}
    className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-burgundy-600 to-burgundy-500 z-50 origin-left"
  />
)

interface PageTransitionProps {
  children: React.ReactNode
  variant?: 'slideUp' | 'fadeScale' | 'slideRight'
  showLoadingBar?: boolean
}

export default function PageTransition({
  children,
  variant = 'slideUp',
  showLoadingBar = true
}: PageTransitionProps) {
  const pathname = usePathname()
  const [isTransitioning, setIsTransitioning] = useState(false)
  const selectedVariant = transitionVariants[variant]

  useEffect(() => {
    // Smooth transition on route change
    setIsTransitioning(true)

    // Scroll to top smoothly
    window.scrollTo({ top: 0, behavior: 'smooth' })

    const timer = setTimeout(() => {
      setIsTransitioning(false)
    }, 200)

    return () => clearTimeout(timer)
  }, [pathname])

  return (
    <>
      {/* Loading Bar */}
      <AnimatePresence>
        {isTransitioning && showLoadingBar && <LoadingBar />}
      </AnimatePresence>

      {/* Page Content with Transition */}
      <AnimatePresence mode="wait">
        <motion.div
          key={pathname}
          initial={selectedVariant.initial}
          animate={selectedVariant.animate}
          exit={selectedVariant.exit}
          transition={selectedVariant.transition}
          className="min-h-screen"
        >
          {children}
        </motion.div>
      </AnimatePresence>
    </>
  )
}
