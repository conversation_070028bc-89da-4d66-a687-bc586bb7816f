import { Metadata } from "next"
import ServicesContent from "./services-content"

export const metadata: Metadata = {
  title: "Our Services - Dwelling Desire | Comprehensive Real Estate Solutions in Ahmedabad",
  description: "Explore Dwelling Desire's complete range of real estate services including residential & commercial property buying, selling, leasing, project marketing, interior consultation, and bulk deals in Ahmedabad.",
  keywords: "real estate services ahmedabad, property buying selling, commercial leasing, project marketing, interior consultation, vastu consultation, bulk deals, group booking, dwelling desire services",
  openGraph: {
    title: "Complete Real Estate Services - Dwelling Desire",
    description: "From residential properties to commercial spaces, project marketing to interior consultation - we offer comprehensive real estate solutions in Ahmedabad.",
    images: ["/images/services.webp"],
  },
}

export default function ServicesPage() {
  return <ServicesContent />
}
