import { <PERSON>ada<PERSON> } from "next"
import { notFound } from "next/navigation"
import ProjectDetailContent from "./project-detail-content"
import { allProjects } from "../projects-data"

// Use the shared projects data

interface Props {
  params: Promise<{ id: string }>
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { id } = await params
  // Try to find project by slug first, then by ID
  let project = allProjects.find(p => p.slug === id)
  if (!project) {
    project = allProjects.find(p => p.id === parseInt(id))
  }

  if (!project) {
    return {
      title: "Project Not Found - Dwelling Desire",
      description: "The requested project could not be found."
    }
  }

  return {
    title: `${project.title} - ${project.location} | Dwelling Desire`,
    description: `${project.description || `${project.title} in ${project.location}`} Price: ${project.price}. ${project.beds} apartments in ${project.location}.`,
    keywords: `${project.title}, ${project.location}, ${project.type}, ${project.beds}, dwelling desire, ahmedabad properties`,
    openGraph: {
      title: `${project.title} - ${project.location}`,
      description: project.description || `${project.title} in ${project.location}`,
      images: [project.image],
    },
  }
}

export default async function ProjectDetailPage({ params }: Props) {
  const { id } = await params
  // Try to find project by slug first, then by ID
  let project = allProjects.find(p => p.slug === id)
  if (!project) {
    project = allProjects.find(p => p.id === parseInt(id))
  }

  if (!project) {
    notFound()
  }

  return <ProjectDetailContent project={project} />
}
