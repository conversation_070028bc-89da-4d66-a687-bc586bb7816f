import { Metadata } from "next"
import InsightsPageContent from "./insights-content"

export const metadata: Metadata = {
  title: "Real Estate Insights & Market Trends | Dwelling Desire",
  description: "Stay updated with the latest real estate insights, market trends, investment guides, and expert analysis from Dwelling Desire. Get valuable property market information.",
  keywords: "real estate insights, market trends, property investment, Ahmedabad real estate, property guides, market analysis",
  openGraph: {
    title: "Real Estate Insights & Market Trends | Dwelling Desire",
    description: "Expert insights and market trends for informed real estate decisions",
    images: ["/images/insights/insights-og.jpg"],
  },
}

export default function InsightsPage() {
  return <InsightsPageContent />
}
