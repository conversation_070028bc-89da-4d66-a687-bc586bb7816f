"use client"

import { useEffect, useRef } from "react"
import Image from "next/image"

const developers = [
  { name: "Godrej Properties", logo: "/images/developers/godrej.webp" },
  { name: "Sobha Limited", logo: "/images/developers/sobha.webp" },
  { name: "Lodha Group", logo: "/images/developers/lodha.webp" },
  { name: "Aaryan Group", logo: "/images/developers/aaryan.webp" },
  { name: "Aaryan Properties", logo: "/images/developers/aaryan-2.webp" },
  { name: "Gala Group", logo: "/images/developers/gald.webp" },
  { name: "HRG Group", logo: "/images/developers/hrg.webp" },
  { name: "Safal Group", logo: "/images/developers/safal.webp" },
  { name: "Savaliya Group", logo: "/images/developers/savaliya.webp" },
  { name: "Shaligram Group", logo: "/images/developers/shaligram.webp" },
  { name: "Shipl Group", logo: "/images/developers/shipl.webp" },
  { name: "Swagat Group", logo: "/images/developers/swagat.webp" },
  { name: "Swati Group", logo: "/images/developers/swati.webp" },
  { name: "Arista Homes", logo: "/images/developers/arista.webp" },
  { name: "Constera Group", logo: "/images/developers/constera.webp" },
  { name: "Deep Group", logo: "/images/developers/deep.webp" },
  { name: "E Square", logo: "/images/developers/e.webp" },
  { name: "HR Group", logo: "/images/developers/hr.webp" },
  { name: "Pacific Group", logo: "/images/developers/pacific.webp" },
  { name: "PD Group", logo: "/images/developers/pd.webp" },
  { name: "Ratnanjali Group", logo: "/images/developers/ratnanjali.webp" },
  { name: "Venus Group", logo: "/images/developers/venus.webp" },
  { name: "Zaveri Group", logo: "/images/developers/zaveri.webp" },
]

export default function DeveloperLogos() {
  const sectionRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <section ref={sectionRef} className="py-16 bg-textured-light">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12 animate-on-scroll opacity-0 translate-y-8">
          <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-4">
            Trusted by Leading{" "}
            <span className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 bg-clip-text text-transparent">
              Developers
            </span>
          </h3>
          <p className="text-gray-600 max-w-2xl mx-auto">
            We partner with India's most reputed real estate developers to bring you premium properties and investment
            opportunities.
          </p>
        </div>

        <div className="animate-on-scroll opacity-0 translate-y-8 delay-200">
          {/* Scrolling Logo Animation */}
          <div className="relative overflow-hidden group">
            <div className="flex animate-scroll space-x-12 items-center group-hover:pause">
              {/* First set of logos */}
              {developers.map((developer, index) => (
                <div
                  key={`first-${index}`}
                  className="flex-shrink-0 transition-all duration-300 grayscale hover:grayscale-0 opacity-60 hover:opacity-100"
                >
                  <Image
                    src={developer.logo || "/placeholder.svg"}
                    alt={developer.name}
                    width={160}
                    height={80}
                    className="h-20 w-auto object-contain"
                  />
                </div>
              ))}
              {/* Duplicate set for seamless loop */}
              {developers.map((developer, index) => (
                <div
                  key={`second-${index}`}
                  className="flex-shrink-0 transition-all duration-300 grayscale hover:grayscale-0 opacity-60 hover:opacity-100"
                >
                  <Image
                    src={developer.logo || "/placeholder.svg"}
                    alt={developer.name}
                    width={160}
                    height={80}
                    className="h-20 w-auto object-contain"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
