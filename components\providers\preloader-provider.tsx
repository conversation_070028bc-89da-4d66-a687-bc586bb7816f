"use client"

import React, { createContext, useContext, useEffect, useState } from "react"
import Preloader from "@/components/ui/preloader"
import { useLottieAnimation, ANIMATION_PATHS } from "@/hooks/use-lottie-animation"

interface PreloaderContextType {
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
  showPreloader: (duration?: number) => void
  hidePreloader: () => void
}

const PreloaderContext = createContext<PreloaderContextType | undefined>(undefined)

export const usePreloader = () => {
  const context = useContext(PreloaderContext)
  if (!context) {
    throw new Error("usePreloader must be used within a PreloaderProvider")
  }
  return context
}

interface PreloaderProviderProps {
  children: React.ReactNode
  animationData?: any
  initialDuration?: number
  showOnMount?: boolean
}

export const PreloaderProvider: React.FC<PreloaderProviderProps> = ({
  children,
  animationData,
  initialDuration = 2000, // Reduced from 3000 to 2000
  showOnMount = true
}) => {
  const [isLoading, setIsLoading] = useState(false) // Start with false
  const [preloaderDuration, setPreloaderDuration] = useState(initialDuration)
  const [hasShownInitialLoader, setHasShownInitialLoader] = useState(false)

  // Load the default animation if no custom animation is provided
  const { animationData: defaultAnimation } = useLottieAnimation(ANIMATION_PATHS.loading)
  const finalAnimationData = animationData || defaultAnimation

  // Show preloader only on initial page load, not on navigation
  useEffect(() => {
    if (showOnMount && !hasShownInitialLoader) {
      setIsLoading(true)
      setHasShownInitialLoader(true)
    }
  }, [showOnMount, hasShownInitialLoader])

  const showPreloader = (duration = 3000) => {
    setPreloaderDuration(duration)
    setIsLoading(true)
  }

  const hidePreloader = () => {
    setIsLoading(false)
  }

  const handlePreloaderComplete = () => {
    setIsLoading(false)
  }

  // Prevent scrolling when preloader is active
  useEffect(() => {
    if (isLoading) {
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "unset"
    }

    return () => {
      document.body.style.overflow = "unset"
    }
  }, [isLoading])

  const contextValue: PreloaderContextType = {
    isLoading,
    setIsLoading,
    showPreloader,
    hidePreloader
  }

  return (
    <PreloaderContext.Provider value={contextValue}>
      {isLoading && (
        <Preloader
          animationData={finalAnimationData}
          duration={preloaderDuration}
          onComplete={handlePreloaderComplete}
        />
      )}
      <div className={isLoading ? "pointer-events-none" : ""}>
        {children}
      </div>
    </PreloaderContext.Provider>
  )
}

export default PreloaderProvider
