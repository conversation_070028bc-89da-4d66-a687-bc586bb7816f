"use client"

import { useEffect, useRef } from "react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { TextGradientScroll } from "@/components/ui/text-gradient-scroll"
import {
  Target,
  Eye,
  BookOpen,
  Building2,
  Home,
  TrendingUp,
  Shield,
  Award,
  CheckCircle,
  Handshake,
  Star
} from "lucide-react"

export default function AboutContent() {
  const mainRef = useRef<HTMLElement>(null)

  useEffect(() => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -50px 0px"
    }

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.remove("opacity-0", "translate-y-8")
          entry.target.classList.add("opacity-100", "translate-y-0")
        }
      })
    }, observerOptions)

    const animatedElements = mainRef.current?.querySelectorAll(".animate-on-scroll")
    animatedElements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  return (
    <main ref={mainRef} className="min-h-screen">
      {/* Header Section */}
      <section className="relative pt-24 pb-16 lg:pt-28 lg:pb-20 bg-gradient-to-r from-burgundy-600 to-burgundy-700 text-white overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0 bg-[url('/images/hero-bg.jpg')] bg-cover bg-center opacity-20"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-black/30 to-black/20"></div>

        <div className="relative container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="secondary" className="bg-white/20 text-white border-white/30 mb-6 px-4 py-2 text-sm">
              <BookOpen size={16} className="mr-2" />
              About Us
            </Badge>

            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              About <span className="text-yellow-300">Dwelling Desire</span>
            </h1>

            <p className="text-xl text-white/90 max-w-2xl mx-auto">
              Your trusted partner in Ahmedabad's real estate journey with expertise, transparency, and commitment to excellence.
            </p>
          </div>
        </div>
      </section>

      {/* About Content */}
      <section className="py-20 bg-textured-light">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <h2 className="text-3xl font-bold text-gray-900 mb-8 text-center">
                Welcome to <span className="text-burgundy-600">Dwelling Desire</span>
              </h2>

              <div className="text-center mb-12">
                <p className="text-lg text-gray-600 leading-relaxed max-w-3xl mx-auto">
                  Dwelling Desire is a <strong className="text-burgundy-600">premier real estate consulting firm</strong> based in Ahmedabad, specializing in buying, selling, leasing, and investment advisory. With a strong presence in the commercial and residential real estate market, we have successfully served <strong className="text-burgundy-600">1,000+ clients</strong>, helping them make informed and profitable real estate decisions.
                </p>
              </div>

              <div className="text-center mb-16">
                <div className="max-w-3xl mx-auto">
                  <TextGradientScroll
                    text="Founded by Jay Dholakiya and Dhara Dholakiya, our firm operates with a vision to redefine real estate consultancy with integrity, transparency, and expertise. Our team of 16 skilled professionals ensures that every client receives personalized service and strategic insights tailored to their needs."
                    className="text-lg text-gray-600 leading-relaxed justify-center"
                    type="letter"
                    textOpacity="soft"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Expertise Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <div className="text-center mb-16">
                <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
                  <Award size={16} className="mr-2" />
                  Our Expertise
                </Badge>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                  What We <span className="text-burgundy-600">Specialize In</span>
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Comprehensive real estate services tailored to meet your unique property needs
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <Card className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Building2 className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">New Project Sales</h4>
                    <p className="text-gray-600 leading-relaxed">
                      Partnering with leading developers to offer the best upcoming projects with exclusive access and competitive pricing.
                    </p>
                  </CardContent>
                </Card>

                <Card className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-blue-600 to-blue-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Home className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">Luxury & Affordable Housing</h4>
                    <p className="text-gray-600 leading-relaxed">
                      Expertise in both premium and budget-friendly properties to match every lifestyle and budget requirement.
                    </p>
                  </CardContent>
                </Card>

                <Card className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-green-600 to-green-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <TrendingUp className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">Commercial Spaces</h4>
                    <p className="text-gray-600 leading-relaxed">
                      Offices, retail spaces, and investment-friendly commercial properties in prime business locations.
                    </p>
                  </CardContent>
                </Card>

                <Card className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-purple-600 to-purple-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Handshake className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">Leasing & Rentals</h4>
                    <p className="text-gray-600 leading-relaxed">
                      Helping businesses and individuals find the right spaces for rent with flexible terms and conditions.
                    </p>
                  </CardContent>
                </Card>

                <Card className="border-0 bg-textured-white hover:shadow-lg transition-all duration-300 group">
                  <CardContent className="p-8 text-center">
                    <div className="w-16 h-16 mx-auto mb-6 bg-gradient-to-r from-orange-600 to-orange-700 rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                      <Star className="text-white" size={28} />
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-4">Investment Consulting</h4>
                    <p className="text-gray-600 leading-relaxed">
                      Strategic investment opportunities in Ahmedabad's growing real estate market with expert guidance.
                    </p>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose Us Section */}
      <section className="py-20 bg-textured-light">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <div className="text-center mb-16">
                <Badge variant="secondary" className="bg-burgundy-100 text-burgundy-600 border-burgundy-200 mb-4 px-4 py-2">
                  <Shield size={16} className="mr-2" />
                  Why Choose Us
                </Badge>
                <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                  Why <span className="text-burgundy-600">Dwelling Desire</span>?
                </h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Discover what makes us Ahmedabad's most trusted real estate partner
                </p>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
                <div className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm">
                  <div className="bg-burgundy-100 p-3 rounded-xl flex-shrink-0">
                    <CheckCircle className="text-burgundy-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 mb-2">Client-Centric Approach</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Personalized solutions for buyers, sellers, and investors with dedicated support throughout your journey.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm">
                  <div className="bg-blue-100 p-3 rounded-xl flex-shrink-0">
                    <CheckCircle className="text-blue-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 mb-2">Market Expertise</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Deep knowledge of Ahmedabad's real estate landscape with insights into emerging trends and opportunities.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm">
                  <div className="bg-green-100 p-3 rounded-xl flex-shrink-0">
                    <CheckCircle className="text-green-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 mb-2">Strong Developer Network</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Exclusive access to top projects and investment deals through our established developer partnerships.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm">
                  <div className="bg-purple-100 p-3 rounded-xl flex-shrink-0">
                    <CheckCircle className="text-purple-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 mb-2">End-to-End Support</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      From property selection to final transaction and after-sales assistance - we're with you every step.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm">
                  <div className="bg-orange-100 p-3 rounded-xl flex-shrink-0">
                    <CheckCircle className="text-orange-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 mb-2">Trusted by Leading Brands</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Our work with industry leaders and satisfied clients speaks for our commitment to excellence.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4 p-6 bg-white rounded-xl shadow-sm">
                  <div className="bg-red-100 p-3 rounded-xl flex-shrink-0">
                    <CheckCircle className="text-red-600" size={24} />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-900 mb-2">1000+ Happy Clients</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      Successfully served over 1000 clients with transparent dealings and professional service.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mission & Vision Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="animate-on-scroll opacity-0 translate-y-8">
              <div className="grid lg:grid-cols-2 gap-12">
                {/* Vision Statement */}
                <div className="text-center lg:text-left">
                  <div className="w-16 h-16 bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-full flex items-center justify-center mb-6 mx-auto lg:mx-0">
                    <Eye className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-6">
                    Our <span className="text-burgundy-600">Vision</span>
                  </h3>
                  <p className="text-lg text-gray-600 leading-relaxed mb-6">
                    To become the most trusted real estate consulting firm by offering personalized, one-to-one solutions that guide our clients through every step of their property journey — from selection to possession and beyond.
                  </p>
                </div>

                {/* Mission Statement */}
                <div className="text-center lg:text-left">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center mb-6 mx-auto lg:mx-0">
                    <Target className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-2xl lg:text-3xl font-bold text-gray-900 mb-6">
                    Our <span className="text-burgundy-600">Mission</span>
                  </h3>
                  <p className="text-lg text-gray-600 leading-relaxed mb-6">
                    At Dwelling Desire, our mission is to simplify real estate decisions through tailored services that include:
                  </p>
                  <ul className="text-gray-600 space-y-3 text-left">
                    <li className="flex items-start">
                      <CheckCircle className="text-burgundy-600 mr-3 mt-1 flex-shrink-0" size={16} />
                      <span>Customized property solutions for every client</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="text-burgundy-600 mr-3 mt-1 flex-shrink-0" size={16} />
                      <span>In-house Vastu consultation for harmonious living and working spaces</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="text-burgundy-600 mr-3 mt-1 flex-shrink-0" size={16} />
                      <span>Expert guidance in home loan and interior design for a complete experience</span>
                    </li>
                  </ul>
                  <p className="text-gray-600 leading-relaxed mt-6">
                    We are committed to professionalism, transparency, and long-term relationships built on trust and performance.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}
