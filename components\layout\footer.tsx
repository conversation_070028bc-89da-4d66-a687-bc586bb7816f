import Link from "next/link"
import Image from "next/image"
import { MapPin, Phone, Mail, Facebook, Twitter, Instagram, Linkedin, Youtube, ArrowRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer */}
      <div className="container mx-auto px-4 py-16">
        <div className="grid lg:grid-cols-4 md:grid-cols-2 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <Link href="/" className="inline-block mb-6">
              <Image
                src="/images/logo.png"
                alt="Dwelling Desire"
                width={180}
                height={60}
                className="h-20 w-auto brightness-0 invert"
              />
            </Link>
            <p className="text-gray-400 mb-6 leading-relaxed">
              Your trusted partner in luxury real estate. We transform dreams into reality with premium properties and
              exceptional service.
            </p>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <MapPin className="text-burgundy-400 flex-shrink-0" size={18} />
                <span className="text-sm text-gray-400">Titanium Heights, A-705, Prahlad Nagar, Ahmedabad</span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="text-burgundy-400 flex-shrink-0" size={18} />
                <span className="text-sm text-gray-400">+91 81411 12929</span>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="text-burgundy-400 flex-shrink-0" size={18} />
                <span className="text-sm text-gray-400"><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Quick Links</h3>
            <ul className="space-y-3">
              {[
                { name: "Home", href: "/" },
                { name: "About Us", href: "/about" },
                { name: "Services", href: "/services" },
                { name: "Projects", href: "/projects" },
                { name: "Insights", href: "/insights" },
                { name: "Career", href: "/career" },
                { name: "Contact", href: "/contact" },
              ].map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors duration-300 text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Our Services</h3>
            <ul className="space-y-3">
              {[
                { name: "Buy Property", href: "/services/buy" },
                { name: "Rent Property", href: "/services/rent" },
                { name: "Lease Commercial", href: "/services/lease" },
                { name: "Pre-lease", href: "/services/pre-lease" },
                { name: "Sell Property", href: "/services/sell" },
                { name: "Investment", href: "/services/invest" },
                { name: "Property Management", href: "/services/management" },
              ].map((service) => (
                <li key={service.name}>
                  <Link
                    href={service.href}
                    className="text-gray-400 hover:text-white transition-colors duration-300 text-sm"
                  >
                    {service.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Stay Updated</h3>
            <p className="text-gray-400 mb-4 text-sm">
              Subscribe to our newsletter for the latest property updates and market insights.
            </p>
            <div className="space-y-4">
              <div className="flex">
                <Input
                  type="email"
                  placeholder="Your email address"
                  className="rounded-l-lg rounded-r-none border-gray-600 bg-gray-800 text-white placeholder-gray-400 focus:border-burgundy-500"
                />
                <Button className="rounded-l-none rounded-r-lg bg-burgundy-600 hover:bg-burgundy-700 px-4">
                  <ArrowRight size={16} />
                </Button>
              </div>
            </div>

            {/* Social Links */}
            <div className="mt-8">
              <h4 className="text-sm font-semibold mb-4">Follow Us</h4>
              <div className="flex space-x-4">
                {[
                  { icon: Facebook, href: "https://www.facebook.com/share/1S9KHZS8PW/?mibextid=wwXIfr", color: "hover:text-blue-400" },
                  { icon: Instagram, href: "https://www.instagram.com/dwellingdesire?igsh=ZmNyZHYwMXNiNjlr", color: "hover:text-pink-400" },
                  { icon: Linkedin, href: "https://www.linkedin.com/in/dhara-pujara-2b8443164?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=ios_app", color: "hover:text-blue-400" },
                ].map((social, index) => {
                  const IconComponent = social.icon
                  return (
                    <Link
                      key={index}
                      href={social.href}
                      className={`text-gray-400 ${social.color} transition-colors duration-300`}
                    >
                      <IconComponent size={20} />
                    </Link>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-sm text-gray-400">© 2025 Dwelling Desire. All rights reserved.</div>
            <div className="flex space-x-6 text-sm">
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                Privacy Policy
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">
                Terms of Service
              </Link>
              <Link href="/sitemap" className="text-gray-400 hover:text-white transition-colors">
                Sitemap
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
