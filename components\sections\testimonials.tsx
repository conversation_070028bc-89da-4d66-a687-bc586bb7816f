"use client"

import { useEffect, useRef, useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Star, Quote, ChevronLeft, ChevronRight } from "lucide-react"
import Image from "next/image"

const testimonials = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    role: "Business Owner",
    location: "Prahlad Nagar",
    image: "/placeholder.svg?height=60&width=60&text=RP",
    rating: 5,
    text: "Great service, very polite and service oriented. Helpful property consultant with ample options available for every requirement. Will definitely stay in touch for future requirements as well. Had a really good experience with <PERSON><PERSON><PERSON>, <PERSON> and most importantly Mr. <PERSON><PERSON><PERSON>.",
    property: "3 BHK Apartment",
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    role: "Software Engineer",
    location: "Satellite",
    image: "/placeholder.svg?height=60&width=60&text=PS",
    rating: 5,
    text: "Excellent service from start to finish. They understood our requirements perfectly and found us the perfect investment property.",
    property: "2 BHK Investment Property",
  },
  {
    id: 3,
    name: "<PERSON><PERSON>",
    role: "Entrepreneur",
    location: "SG Highway",
    image: "/placeholder.svg?height=60&width=60&text=AD",
    rating: 5,
    text: "Professional, transparent, and reliable. Dwelling Desire made our commercial space acquisition smooth and hassle-free.",
    property: "Commercial Office Space",
  },
  {
    id: 4,
    name: "Neha Joshi",
    role: "Doctor",
    location: "Bodakdev",
    image: "/placeholder.svg?height=60&width=60&text=NJ",
    rating: 5,
    text: "Outstanding service! They helped us sell our property at the best market price. Their marketing strategy is top-notch.",
    property: "4 BHK Villa",
  },
  {
    id: 5,
    name: "Kiran Modi",
    role: "CA",
    location: "Vastrapur",
    image: "/placeholder.svg?height=60&width=60&text=KM",
    rating: 5,
    text: "Dwelling Desire exceeded our expectations. From property search to final documentation, everything was handled professionally.",
    property: "2 BHK Apartment",
  },
]

export default function Testimonials() {
  const sectionRef = useRef<HTMLElement>(null)
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(true)

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-fade-in-up")
          }
        })
      },
      { threshold: 0.1 },
    )

    const elements = sectionRef.current?.querySelectorAll(".animate-on-scroll")
    elements?.forEach((el) => observer.observe(el))

    return () => observer.disconnect()
  }, [])

  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length)
    }, 5000)

    return () => clearInterval(interval)
  }, [isAutoPlaying])

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length)
    setIsAutoPlaying(false)
  }

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length)
    setIsAutoPlaying(false)
  }

  const goToTestimonial = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
  }

  return (
    <section ref={sectionRef} className="py-16 bg-gradient-to-br from-burgundy-50 to-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-12 animate-on-scroll opacity-0 translate-y-8">
          <div className="inline-flex items-center space-x-2 bg-burgundy-100 text-burgundy-700 rounded-full px-4 py-2 text-sm font-medium mb-4">
            <Star size={16} />
            <span>Client Testimonials</span>
          </div>

          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            What Our{" "}
            <span className="relative">
              <span className="bg-gradient-to-r from-burgundy-600 to-burgundy-700 bg-clip-text text-transparent">
                Clients
              </span>
              <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-full"></div>
            </span>{" "}
            Say
          </h2>

          <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            Don't just take our word for it. Here's what our satisfied clients have to say.
          </p>
        </div>

        {/* Testimonials Carousel */}
        <div className="animate-on-scroll opacity-0 translate-y-8 delay-200">
          <div className="relative max-w-3xl mx-auto">
            {/* Main Testimonial Card */}
            <Card className="border-0 bg-white/80 backdrop-blur-sm shadow-xl">
              <CardContent className="p-6 lg:p-8">
                <div className="text-center px-6">
                  {/* Quote Icon */}
                  <div className="w-12 h-12 bg-gradient-to-r from-burgundy-600 to-burgundy-700 rounded-full flex items-center justify-center mx-auto mb-6">
                    <Quote className="text-white" size={20} />
                  </div>

                  {/* Rating */}
                  <div className="flex justify-center mb-4">
                    {[...Array(testimonials[currentIndex].rating)].map((_, i) => (
                      <Star key={i} className="text-yellow-400 fill-current" size={18} />
                    ))}
                  </div>

                  {/* Testimonial Text */}
                  <blockquote className="text-md md:text-md text-gray-700 leading-relaxed mb-6 font-medium px-3.5">
                    "{testimonials[currentIndex].text}"
                  </blockquote>

                  {/* Client Info */}
                  <div className="flex items-center justify-center space-x-4">
                    <Image
                      src={testimonials[currentIndex].image || "/placeholder.svg"}
                      alt={testimonials[currentIndex].name}
                      width={60}
                      height={60}
                      className="rounded-full border-4 border-burgundy-100"
                    />
                    <div className="text-left">
                      <div className="font-bold text-gray-900 text-lg">{testimonials[currentIndex].name}</div>
                      <div className="text-gray-600 text-sm">{testimonials[currentIndex].role}</div>
                      <div className="text-xs text-burgundy-600 font-medium">
                        {testimonials[currentIndex].property} • {testimonials[currentIndex].location}
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Navigation Buttons */}
            <Button
              variant="outline"
              size="icon"
              className="absolute left-2 lg:left-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm border-gray-200 hover:bg-white hover:border-burgundy-300 rounded-full w-10 h-10 lg:w-12 lg:h-12"
              onClick={prevTestimonial}
            >
              <ChevronLeft size={18} />
            </Button>

            <Button
              variant="outline"
              size="icon"
              className="absolute right-2 lg:right-4 top-1/2 transform -translate-y-1/2 bg-white/90 backdrop-blur-sm border-gray-200 hover:bg-white hover:border-burgundy-300 rounded-full w-10 h-10 lg:w-12 lg:h-12"
              onClick={nextTestimonial}
            >
              <ChevronRight size={18} />
            </Button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center mt-6 space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 lg:w-3 lg:h-3 rounded-full transition-all duration-300 ${
                  index === currentIndex ? "bg-burgundy-600 w-6 lg:w-8" : "bg-gray-300 hover:bg-gray-400"
                }`}
                onClick={() => goToTestimonial(index)}
              />
            ))}
          </div>
        </div>

        {/* Stats Row */}
        <div className="animate-on-scroll opacity-0 translate-y-8 delay-400 mt-12">
          <div className="grid grid-cols-3 gap-4 lg:gap-8 text-center">
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 lg:p-6">
              <div className="text-2xl lg:text-3xl font-bold text-burgundy-600 mb-2">500+</div>
              <div className="text-sm lg:text-base text-gray-600 font-medium">Happy Clients</div>
            </div>
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 lg:p-6">
              <div className="text-2xl lg:text-3xl font-bold text-burgundy-600 mb-2">4.9/5</div>
              <div className="text-sm lg:text-base text-gray-600 font-medium">Average Rating</div>
            </div>
            <div className="bg-white/60 backdrop-blur-sm rounded-2xl p-4 lg:p-6">
              <div className="text-2xl lg:text-3xl font-bold text-burgundy-600 mb-2">98%</div>
              <div className="text-sm lg:text-base text-gray-600 font-medium">Client Satisfaction</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
