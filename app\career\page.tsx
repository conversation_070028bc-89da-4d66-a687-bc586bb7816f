import type { Metadata } from "next"
import CareerPageContent from "./career-content"

export const metadata: Metadata = {
  title: "Career - Join Dwelling Desire | Real Estate Jobs in Ahmedabad",
  description: "Join Dwelling Desire's dynamic team in Ahmedabad. Explore exciting career opportunities in real estate, sales, marketing, and property management. Build your future with Gujarat's leading real estate company.",
  keywords: "career dwelling desire, real estate jobs ahmedabad, property jobs gujarat, sales career real estate, real estate agent jobs, property consultant jobs ahmedabad",
  openGraph: {
    title: "Career at Dwelling Desire - Build Your Future in Real Estate",
    description: "Join our passionate team and grow your career in Ahmedabad's premier real estate company. Exciting opportunities await!",
    url: "/career",
    images: [
      {
        url: "/images/logo.png",
        width: 1200,
        height: 630,
        alt: "Career at Dwelling Desire",
      },
    ],
  },
}

export default function CareerPage() {
  return <CareerPageContent />
}
